{"name": "@designable/sandbox-multi-workspace-example", "version": "1.0.0-beta.45", "license": "MIT", "private": true, "engines": {"npm": ">=3.0.0"}, "scripts": {"build": "rimraf dist && webpack-cli --config config/webpack.prod.ts", "start": "webpack-dev-server --config config/webpack.dev.ts"}, "devDependencies": {"file-loader": "^5.0.2", "fs-extra": "^8.1.0", "html-webpack-plugin": "^3.2.0", "mini-css-extract-plugin": "^1.6.0", "raw-loader": "^4.0.0", "style-loader": "^1.1.3", "ts-loader": "^7.0.4", "typescript": "4.1.5", "webpack": "^4.41.5", "webpack-bundle-analyzer": "^3.9.0", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.10.1"}, "dependencies": {"@designable/core": "1.0.0-beta.45", "@designable/react": "1.0.0-beta.45", "@designable/react-sandbox": "1.0.0-beta.45", "@designable/react-settings-form": "1.0.0-beta.45", "@designable/shared": "1.0.0-beta.45", "@formily/reactive": "^2.0.2", "@formily/reactive-react": "^2.0.2", "antd": "^4.15.2", "react": "^16.8.x", "react-dom": "^16.8.x", "react-jss": "^10.4.0"}, "gitHead": "820790a9ae32c2348bb36b3de7ca5f1051ed392c"}