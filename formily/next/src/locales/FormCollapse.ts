export const FormCollapse = {
  'zh-CN': {
    title: '折叠面板',
    addCollapsePanel: '添加面板',
    settings: {
      'x-component-props': {
        accordion: {
          title: '手风琴模式',
          tooltip: '启用后一次只能展开一个',
        },
      },
    },
  },
  'en-US': {
    title: 'Collapse',
    addCollapsePanel: 'Add Panel',
    settings: {
      'x-component-props': {
        accordion: {
          title: 'Accordion',
          tooltip: 'When enabled, only one can be expanded at a time',
        },
      },
    },
  },
}

export const FormCollapsePanel = {
  'zh-CN': {
    title: '面板',
  },
  'en-US': {
    title: 'Panel',
  },
}
