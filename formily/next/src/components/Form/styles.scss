@import '~@alifd/next/lib/core/style/global';

$next-prefix: $css-prefix;

@mixin none-pointer-events($selector, $important: true) {
  #{$selector} {
    pointer-events: none #{if($important, !important, '')};
  }
}

@mixin designable-bootstrap($components...) {
  .dn-designable-form {
    @each $component in $components {
      $selector: #{$next-prefix}#{$component};
      @include none-pointer-events('.#{$selector}, .#{$selector} input');
    }
    @include none-pointer-events('.anticon svg', false);
  }
}

@include designable-bootstrap(
  'input',
  'input-textarea',
  'select',
  'radio-group',
  'range',
  'cascader',
  'switch',
  'checkbox-group',
  'date-picker',
  'range-picker',
  'month-picker',
  'year-picker',
  'week-picker',
  'time-picker',
  'rating',
  'transfer',
  'tree-select',
  'upload'
);
