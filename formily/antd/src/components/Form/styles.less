@import '~antd/lib/style/themes/default.less';

.dn-designable-form {
  .@{ant-prefix}-input,
  .@{ant-prefix}-input-number,
  .@{ant-prefix}-input-affix-wrapper,
  .@{ant-prefix}-cascader-picker,
  .@{ant-prefix}-picker-input,
  .@{ant-prefix}-picker,
  .@{ant-prefix}-cascader-picker-label,
  .@{ant-prefix}-slider,
  .@{ant-prefix}-checkbox,
  .@{ant-prefix}-rate,
  .@{ant-prefix}-switch,
  .@{ant-prefix}-radio,
  .@{ant-prefix}-radio-wrapper,
  .@{ant-prefix}-checkbox-group,
  .@{ant-prefix}-checkbox-wrapper,
  .@{ant-prefix}-radio-group,
  .@{ant-prefix}-upload,
  .@{ant-prefix}-transfer,
  .@{ant-prefix}-select,
  .@{ant-prefix}-select-selector {
    pointer-events: none !important;

    input {
      pointer-events: none !important;
    }
  }

  .anticon svg {
    pointer-events: none;
  }
}
