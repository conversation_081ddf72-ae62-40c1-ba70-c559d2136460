export const FormGrid = {
  'zh-CN': {
    title: '网格布局',
    addGridColumn: '添加网格列',
    settings: {
      'x-component-props': {
        minWidth: '最小宽度',
        minColumns: '最小列数',
        maxWidth: '最大宽度',
        maxColumns: '最大列数',
        breakpoints: '响应式断点',
        columnGap: '列间距',
        rowGap: '行间距',
        colWrap: '自动换行',
      },
    },
  },
  'en-US': {
    title: 'Grid',
    addGridColumn: 'Add Grid Column',
    settings: {
      'x-component-props': {
        minWidth: 'Min Width',
        minColumns: 'Min Columns',
        maxWidth: 'Max Width',
        maxColumns: 'Max Columns',
        breakpoints: 'Breakpoints',
        columnGap: 'Column Gap',
        rowGap: 'Row Gap',
        colWrap: 'Col Wrap',
      },
    },
  },
  'ko-KR': {
    title: '그리드 열',
    addGridColumn: '그리드 열 추가',
    settings: {
      'x-component-props': {
        minWidth: '최소 너비',
        minColumns: '최소 열 개수',
        maxWidth: '최대 너비',
        maxColumns: '최대 열 개수',
        breakpoints: '중단점',
        columnGap: '열 간격',
        rowGap: '행 간격',
        colWrap: '자동 줄바꿈',
      },
    },
  },
}

export const FormGridColumn = {
  'zh-CN': {
    title: '网格列',
    settings: {
      'x-component-props': {
        gridSpan: '跨列栏数',
      },
    },
  },
  'en-US': {
    title: 'Grid Column',
    settings: {
      'x-component-props': {
        gridSpan: 'Grid Span',
      },
    },
  },
  'ko-KR': {
    title: '그리드 열',
    settings: {
      'x-component-props': {
        gridSpan: '그리드 스팬',
      },
    },
  },
}
