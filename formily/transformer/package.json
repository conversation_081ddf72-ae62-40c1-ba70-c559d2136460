{"name": "@designable/formily-transformer", "version": "1.0.0-beta.45", "license": "MIT", "main": "lib", "engines": {"npm": ">=3.0.0"}, "module": "esm", "repository": {"type": "git", "url": "git+https://github.com/alibaba/designable.git"}, "types": "esm/index.d.ts", "bugs": {"url": "https://github.com/alibaba/designable/issues"}, "homepage": "https://github.com/alibaba/designable#readme", "scripts": {"build": "rimraf -rf lib esm dist && npm run build:cjs && npm run build:esm", "build:cjs": "tsc --project tsconfig.build.json", "build:esm": "tsc --project tsconfig.build.json --module es2015 --outDir esm"}, "publishConfig": {"access": "public"}, "devDependencies": {"@formily/core": "^2.0.2", "@formily/json-schema": "^2.0.2"}, "peerDependencies": {"@formily/core": "^2.0.2", "@formily/json-schema": "^2.0.2"}, "dependencies": {"@designable/core": "1.0.0-beta.45", "@designable/shared": "1.0.0-beta.45"}, "gitHead": "bda070c137ba0003cc4451b2208e089d2e326b23"}