name: Node CI

# on:
#   push:
#     branches:
#       - main
#   pull_request:
#     branches:
#       - main

# jobs:
#   build:
#     runs-on: ${{ matrix.os }}
#     strategy:
#       matrix:
#         node_version: [10.x, 11.x]
#         os: [ubuntu-latest]
#     steps:
#       - uses: actions/checkout@v1
#       - name: Use Node.js ${{ matrix.node_version }}
#         uses: actions/setup-node@v1
#         with:
#           node-version: ${{ matrix.node_version }}

#       - run: yarn -v
#       - run: yarn --ignore-engines
#       - run: yarn build
#       - run: yarn test:prod
#         env:
#           CI: true
#           HEADLESS: false
#           PROGRESS: none
#           NODE_ENV: test
#           NODE_OPTIONS: --max_old_space_size=4096

#       - name: Upload coverage to Codecov
#         uses: codecov/codecov-action@v1
#         with:
#           token: ${{ secrets.CODECOV_TOKEN }}
