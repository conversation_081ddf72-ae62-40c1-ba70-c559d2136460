# Changelog

## v1.0.0-beta.44(2022-02-16)

### :tada: Enhancements

1. [feat: add hasChildren parameter for DroppableWidget (#219)](https://github.com/alibaba/designable/commit/d04cf3a) :point_right: ( [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON><PERSON>) )

1. [feat: add ssr support (#186)](https://github.com/alibaba/designable/commit/6851c71) :point_right: ( [月落音阑](https://github.com/月落音阑) )

1. [feat(core/react/playground): add freelayout feature. add custom source icon feature (#184)](https://github.com/alibaba/designable/commit/171c64f) :point_right: ( [jinphic](https://github.com/jinphic) )

### :beetle: Bug Fixes

1. [fix(react): fix IconWidget side errors(#205)](https://github.com/alibaba/designable/commit/29df2e2) :point_right: ( [yun zhou](https://github.com/yun zhou) )

1. [fix(core): fix web components compatibility in KeyboardDriver (#213)](https://github.com/alibaba/designable/commit/ba7dc10) :point_right: ( [febugcoder](https://github.com/febugcoder) )

1. [fix(core/react): some bug fix about viewport (#198)](https://github.com/alibaba/designable/commit/40abbc2) :point_right: ( [coder-byte](https://github.com/coder-byte) )

1. [fix(playground): fix browser compatibility (#192)](https://github.com/alibaba/designable/commit/57036b9) :point_right: ( [翁立鑫](https://github.com/翁立鑫) )

1. [fix(formily-antd): fix form-grid stuck with repeat key](https://github.com/alibaba/designable/commit/1f9a758) :point_right: ( [yuanxiaoyu](https://github.com/yuanxiaoyu) )

1. [fix(shared/event): fix call subscribeWith infinite recursion (#178)](https://github.com/alibaba/designable/commit/dd06bfa) :point_right: ( [QYW](https://github.com/QYW) )

### :memo: Documents Changes

1. [docs: update package readme](https://github.com/alibaba/designable/commit/118205c) :point_right: ( [Janry](https://github.com/Janry) )

### :blush: Other Changes

1. [chore: reset selected to top tree when redo](https://github.com/alibaba/designable/commit/3b3f5c3) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [chore: update schema configs](https://github.com/alibaba/designable/commit/432a89f) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [chore: add translate example code](https://github.com/alibaba/designable/commit/c45fea6) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.43(2021-12-23)

### :tada: Enhancements

1. [feat(react): add some icons](https://github.com/alibaba/designable/commit/341422b) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.42(2021-12-20)

### :tada: Enhancements

1. [feat: support setNpmCDNRegistry](https://github.com/alibaba/designable/commit/4c38484) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.41(2021-12-16)

### :beetle: Bug Fixes

1. [fix: fix isBehavior](https://github.com/alibaba/designable/commit/fc88f3b) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [fix(formily): set default language (#163)](https://github.com/alibaba/designable/commit/b7668b4) :point_right: ( [Minfee](https://github.com/Minfee) )

1. [fix(react): remove the cleanup of ShadowSVG's effect (#162)](https://github.com/alibaba/designable/commit/b982a2e) :point_right: ( [JuFeng Zhang](https://github.com/JuFeng Zhang) )

## v1.0.0-beta.40(2021-12-08)

### :beetle: Bug Fixes

1. [fix(react): fix NodePathWidget style](https://github.com/alibaba/designable/commit/814e5e6) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [fix(react): fix prettier cdn version](https://github.com/alibaba/designable/commit/e6e0180) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :blush: Other Changes

1. [chore: add sandbox example](https://github.com/alibaba/designable/commit/624076f) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.39(2021-12-06)

### :beetle: Bug Fixes

1. [fix(react): create attach-shadow repeatedly (#161)](https://github.com/alibaba/designable/commit/f8cbc7a) :point_right: ( [WhiteSJ](https://github.com/WhiteSJ) )

## v1.0.0-beta.38(2021-12-03)

### :tada: Enhancements

1. [feat: support resizable to designerProps](https://github.com/alibaba/designable/commit/0215c27) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :beetle: Bug Fixes

1. [fix(react): fix position props in StudioPanel (#155)](https://github.com/alibaba/designable/commit/30aa824) :point_right: ( [vimvinter](https://github.com/vimvinter) )

1. [fix(react): fix component-tree styles](https://github.com/alibaba/designable/commit/a9943dd) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :blush: Other Changes

1. [chore: improve node reisze](https://github.com/alibaba/designable/commit/f71a1f4) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.37(2021-12-01)

### :tada: Enhancements

1. [refactor: remove focusNode feature](https://github.com/alibaba/designable/commit/2d46b94) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [feat(react): add position in layout (#145)](https://github.com/alibaba/designable/commit/baba792) :point_right: ( [programmerwy](https://github.com/programmerwy) )

### :beetle: Bug Fixes

1. [fix(setter): initial bug (#140)](https://github.com/alibaba/designable/commit/4b72dfe) :point_right: ( [Sun HaoWei](https://github.com/Sun HaoWei) )

1. [fix(antd/next): fix locales](https://github.com/alibaba/designable/commit/0bcb178) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :rose: Improve code quality

1. [refactor: remove focusNode feature](https://github.com/alibaba/designable/commit/2d46b94) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :blush: Other Changes

1. [chore: improve code](https://github.com/alibaba/designable/commit/bd28ce6) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [chore(antd/next): update text component output (#147)](https://github.com/alibaba/designable/commit/fa48a6e) :point_right: ( [WhiteSJ](https://github.com/WhiteSJ) )

## v1.0.0-beta.36(2021-11-26)

### :beetle: Bug Fixes

1. [fix(react-settings-form): fix typings](https://github.com/alibaba/designable/commit/2a837fe) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :blush: Other Changes

1. [chore: update formily to 2.0.2](https://github.com/alibaba/designable/commit/89a1725) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.35(2021-11-26)

### :tada: Enhancements

1. [feat(react-settings-form): add exclude inherit in BoxStyleSetter/BoxShadowStyleSetter (#133)](https://github.com/alibaba/designable/commit/d717ebe) :point_right: ( [programmerwy](https://github.com/programmerwy) )

1. [feat(react-settings-form): update sizeinput (#131)](https://github.com/alibaba/designable/commit/a66bace) :point_right: ( [programmerwy](https://github.com/programmerwy) )

1. [feat(language): add Korean translation (#129)](https://github.com/alibaba/designable/commit/9f5cec9) :point_right: ( [2jun0](https://github.com/2jun0) )

### :beetle: Bug Fixes

1. [fix: fix createVoidSchema](https://github.com/alibaba/designable/commit/b3ada9f) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [fix(formily-setters): fix duplicate key warn in ReactionSetter (#132)](https://github.com/alibaba/designable/commit/0f316ea) :point_right: ( [vimvinter](https://github.com/vimvinter) )

### :blush: Other Changes

1. [chore: improve playground code](https://github.com/alibaba/designable/commit/6df7210) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.34(2021-11-13)

### :tada: Enhancements

1. [feat(react): support config css variables](https://github.com/alibaba/designable/commit/e954d86) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :beetle: Bug Fixes

1. [fix(formily): fix reactionSetters and playground](https://github.com/alibaba/designable/commit/87971f4) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [fix(react): fix missing record props in ArrayBase.Item (#125)](https://github.com/alibaba/designable/commit/c041167) :point_right: ( [haloworld](https://github.com/haloworld) )

## v1.0.0-beta.33(2021-11-09)

### :tada: Enhancements

1. [feat(settings-form): add DrawerSetter's trigger props (#122)](https://github.com/alibaba/designable/commit/003de57) :point_right: ( [nekic](https://github.com/nekic) )

1. [feat(formily-setters): add config to DataSourceSetter (#121)](https://github.com/alibaba/designable/commit/6fdf887) :point_right: ( [Sun HaoWei](https://github.com/Sun HaoWei) )

### :beetle: Bug Fixes

1. [fix(settings-form): fix styles](https://github.com/alibaba/designable/commit/6583cf9) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :blush: Other Changes

1. [chore(formily-setters): remove ip/qq format](https://github.com/alibaba/designable/commit/460782d) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.32(2021-10-29)

### :tada: Enhancements

1. [feat(core): support fromUser for SelectionEvent](https://github.com/alibaba/designable/commit/c97de88) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.31(2021-10-29)

### :tada: Enhancements

1. [feat(core): support fromUser for SelectionEvent](https://github.com/alibaba/designable/commit/d3aeda7) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.30(2021-10-27)

### :tada: Enhancements

1. [feat: add source icons](https://github.com/alibaba/designable/commit/56321bc) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.29(2021-10-25)

### :tada: Enhancements

1. [feat(formily-transformer): remove designable core code dependency](https://github.com/alibaba/designable/commit/be8b182) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.28(2021-10-24)

### :tada: Enhancements

1. [feat(react-settings-form): support to expression](https://github.com/alibaba/designable/commit/7290811) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.27(2021-10-23)

### :tada: Enhancements

1. [feat(react): support placeholder to DroppableWidget](https://github.com/alibaba/designable/commit/79ae6d1) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.26(2021-10-23)

### :tada: Enhancements

1. [feat(core): add takeSnapshot to content editable](https://github.com/alibaba/designable/commit/ec82636) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [feat(react): support placeholder to DroppableWidget](https://github.com/alibaba/designable/commit/19e61ad) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.25(2021-10-18)

### :beetle: Bug Fixes

1. [fix(react): add source icons](https://github.com/alibaba/designable/commit/8cb04ec) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.24(2021-10-18)

### :beetle: Bug Fixes

1. [fix(react): fix CompositePanel styles](https://github.com/alibaba/designable/commit/7e2d15f) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [fix(react): fix conflict of registerDesignerBehaviors](https://github.com/alibaba/designable/commit/a88611a) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.23(2021-10-15)

### :beetle: Bug Fixes

1. [fix(react): fix CompositePanel onChange](https://github.com/alibaba/designable/commit/d928b69) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.22(2021-10-14)

### :beetle: Bug Fixes

1. [fix(react): fix CompositePanel](https://github.com/alibaba/designable/commit/a364532) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.21(2021-10-14)

### :beetle: Bug Fixes

1. [fix(react): fix CompositePanel](https://github.com/alibaba/designable/commit/bf4ba3f) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.20(2021-10-08)

### :beetle: Bug Fixes

1. [fix(react): fix CompositePanel](https://github.com/alibaba/designable/commit/84e8dfb) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.19(2021-10-08)

### :beetle: Bug Fixes

1. [fix(react): fix CompositePanel](https://github.com/alibaba/designable/commit/3708613) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.18(2021-10-08)

### :beetle: Bug Fixes

1. [fix(react): fix CompositePanel](https://github.com/alibaba/designable/commit/03eb84d) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.17(2021-10-06)

### :tada: Enhancements

1. [feat(react): add source icons](https://github.com/alibaba/designable/commit/de56564) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :blush: Other Changes

1. [chore(shared): improve element layout calculator](https://github.com/alibaba/designable/commit/f29ec8a) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [chore(core): improve remove](https://github.com/alibaba/designable/commit/8eeed0a) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.16(2021-10-06)

### :blush: Other Changes

1. [chore(core): improve dnd](https://github.com/alibaba/designable/commit/f59b65e) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.15(2021-10-04)

### :beetle: Bug Fixes

1. [fix(react): fix styles](https://github.com/alibaba/designable/commit/19df7b2) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.14(2021-10-04)

### :blush: Other Changes

1. [chore(react): improve theme styles](https://github.com/alibaba/designable/commit/51a0fde) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.13(2021-10-04)

### :blush: Other Changes

1. [chore(react): improve theme styles](https://github.com/alibaba/designable/commit/f45572d) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.12(2021-09-30)

### :tada: Enhancements

1. [feat(react): add more source icons](https://github.com/alibaba/designable/commit/75963da) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.11(2021-09-29)

### :tada: Enhancements

1. [feat(react): support normalize DroppableWidget](https://github.com/alibaba/designable/commit/729410a) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :beetle: Bug Fixes

1. [fix: fix controlled props can not update](https://github.com/alibaba/designable/commit/dcffdeb) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.10(2021-09-27)

### :beetle: Bug Fixes

1. [fix(shared): fix batch add event conflict with multi engine](https://github.com/alibaba/designable/commit/a92f56b) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.9(2021-09-27)

### :beetle: Bug Fixes

1. [fix(core): fix event driver conflict](https://github.com/alibaba/designable/commit/af41004) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

1. [fix(formily): fix FormLayout locales](https://github.com/alibaba/designable/commit/ea2648a) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.8(2021-09-22)

### :beetle: Bug Fixes

1. [fix(react): fix Workspace will throw error when designer is undefined](https://github.com/alibaba/designable/commit/611e9e6) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :rose: Improve code quality

1. [refactor(core): change defaultComponentTree type ITreeNode[] to ITreeNode](https://github.com/alibaba/designable/commit/40b2af7) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.7(2021-09-22)

### :beetle: Bug Fixes

1. [fix(react): fix ui can not refresh with top rerender](https://github.com/alibaba/designable/commit/cf63e82) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.6(2021-09-22)

### :blush: Other Changes

1. [chore(project): move formily deps to peerDeps](https://github.com/alibaba/designable/commit/ee19f25) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

## v1.0.0-beta.5(2021-09-22)

### :beetle: Bug Fixes

1. [fix(formily-antd): fix DatePicker settings](https://github.com/alibaba/designable/commit/2e7e9d2) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )

### :blush: Other Changes

1. [chore(react): update NodePathWidget dependencies](https://github.com/alibaba/designable/commit/87d801e) :point_right: ( [zhili.wzl](https://github.com/zhili.wzl) )
