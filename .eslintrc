{"env": {"node": true}, "extends": ["plugin:react/recommended", "plugin:@typescript-eslint/recommended", "prettier/@typescript-eslint", "plugin:markdown/recommended"], "globals": {"sleep": true, "prettyFormat": true}, "parserOptions": {"ecmaVersion": 10, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react", "prettier", "markdown"], "settings": {"react": {"version": "detect"}}, "rules": {"@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-var-requires": "off"}, "overrides": [{"files": ["**/*.md"], "processor": "markdown/markdown"}, {"files": ["**/*.md/*.{js,tsx}"], "rules": {"@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-unused-vars": "error", "react/display-name": "off", "no-unused-vars": "error", "no-console": "off"}}]}