{"name": "@designable/react-settings-form", "version": "1.0.0-beta.45", "license": "MIT", "main": "lib", "types": "lib/index.d.ts", "engines": {"npm": ">=3.0.0"}, "module": "esm", "repository": {"type": "git", "url": "git+https://github.com/alibaba/designable.git"}, "bugs": {"url": "https://github.com/alibaba/designable/issues"}, "homepage": "https://github.com/alibaba/designable#readme", "scripts": {"build": "rimraf -rf lib esm dist && npm run build:cjs && npm run build:esm && npm run build:umd", "build:cjs": "tsc --project tsconfig.build.json", "build:esm": "tsc --project tsconfig.build.json --module es2015 --outDir esm", "build:umd": "rollup --config"}, "peerDependencies": {"@formily/antd": "^2.0.2", "@formily/core": "^2.0.2", "@formily/react": "^2.0.2", "@formily/reactive": "^2.0.2", "@formily/reactive-react": "^2.0.2", "@formily/shared": "^2.0.2", "antd": "^4.0.0"}, "devDependencies": {"@formily/antd": "^2.0.2", "@formily/core": "^2.0.2", "@formily/react": "^2.0.2", "@formily/reactive": "^2.0.2", "@formily/reactive-react": "^2.0.2", "@formily/shared": "^2.0.2", "antd": "^4.15.5"}, "dependencies": {"@babel/parser": "^7.14.7", "@designable/core": "1.0.0-beta.45", "@designable/react": "1.0.0-beta.45", "@designable/shared": "1.0.0-beta.45", "@monaco-editor/react": "^4.2.1", "monaco-editor": "^0.25.2", "prettier": "^2.3.2", "react-color": "^2.19.3", "react-tiny-popover": "^6.0.5"}, "publishConfig": {"access": "public"}, "gitHead": "bda070c137ba0003cc4451b2208e089d2e326b23"}