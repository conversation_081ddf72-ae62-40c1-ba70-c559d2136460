{"name": "@designable/core", "version": "1.0.0-beta.45", "license": "MIT", "main": "lib", "types": "lib/index.d.ts", "engines": {"npm": ">=3.0.0"}, "module": "esm", "repository": {"type": "git", "url": "git+https://github.com/alibaba/designable.git"}, "bugs": {"url": "https://github.com/alibaba/designable/issues"}, "homepage": "https://github.com/alibaba/designable#readme", "scripts": {"build": "rimraf -rf lib esm dist && npm run build:cjs && npm run build:esm && npm run build:umd", "build:cjs": "tsc --project tsconfig.build.json", "build:esm": "tsc --project tsconfig.build.json --module es2015 --outDir esm", "build:umd": "rollup --config"}, "devDependencies": {"@formily/json-schema": "^2.0.2", "@formily/path": "^2.0.2", "@formily/reactive": "^2.0.2"}, "peerDependencies": {"@formily/json-schema": "^2.0.2", "@formily/path": "^2.0.2", "@formily/reactive": "^2.0.2"}, "dependencies": {"@designable/shared": "1.0.0-beta.45", "@juggle/resize-observer": "^3.3.1"}, "publishConfig": {"access": "public"}, "gitHead": "bda070c137ba0003cc4451b2208e089d2e326b23"}