import React from 'react'

export const Expression = (
  <path d="M206.497462,203.065268 C225.380711,197.940758 240,211.850142 240,233.080254 C240,250.650002 230.253807,260.166948 222.944162,263.09524 C163.857868,285.057424 155.329949,307.751682 155.329949,357.532634 C155.329949,372.174091 156.548223,396.332494 156.548223,411.706023 C156.548223,468.807703 133.401015,496.626471 93.1979695,512 C134.010152,528.105602 156.548223,555.192297 156.548223,612.293977 C156.548223,619.107246 156.308942,627.645952 156.04247,636.317008 L155.98059,638.319645 L155.897583,640.989618 C155.607459,650.323572 155.329949,659.479398 155.329949,666.467366 C155.329949,716.248318 163.857868,738.942576 222.944162,760.90476 C230.253807,763.833052 240,773.349998 240,790.919746 C240,812.149858 225.380711,826.059242 206.497462,820.934732 C112.081218,796.044256 87.106599,750.655741 87.106599,678.912604 C87.106599,673.593637 87.3924294,663.980713 87.687192,654.067212 L87.687192,654.067212 L87.801145,650.223294 C88.0747343,640.939007 88.3248731,631.842609 88.3248731,626.203361 C88.3248731,572.762044 72.4873096,548.603641 31.0659898,547.139496 C11.5736041,545.67535 0,531.765966 0,512 C0,492.234034 11.5736041,478.32465 31.0659898,476.860504 C72.4873096,474.664286 88.3248731,451.237956 88.3248731,397.064567 C88.3248731,391.425318 88.0747343,382.32892 87.801145,373.044633 L87.801145,373.044633 L87.687192,369.200715 C87.3924294,359.287214 87.106599,349.67429 87.106599,344.355323 C87.106599,273.344259 112.081218,227.955744 206.497462,203.065268 Z M386.497462,203.065268 C405.380711,197.940758 420,211.850142 420,233.080254 C420,250.650002 410.253807,260.166948 402.944162,263.09524 C343.857868,285.057424 335.329949,307.751682 335.329949,357.532634 C335.329949,372.174091 336.548223,396.332494 336.548223,411.706023 C336.548223,468.807703 313.401015,496.626471 273.19797,512 C314.010152,528.105602 336.548223,555.192297 336.548223,612.293977 C336.548223,619.107246 336.308942,627.645952 336.04247,636.317008 L335.98059,638.319645 L335.897583,640.989618 C335.607459,650.323572 335.329949,659.479398 335.329949,666.467366 C335.329949,716.248318 343.857868,738.942576 402.944162,760.90476 C410.253807,763.833052 420,773.349998 420,790.919746 C420,812.149858 405.380711,826.059242 386.497462,820.934732 C292.081218,796.044256 267.106599,750.655741 267.106599,678.912604 C267.106599,673.593637 267.392429,663.980713 267.687192,654.067212 L267.687192,654.067212 L267.801145,650.223294 C268.074734,640.939007 268.324873,631.842609 268.324873,626.203361 C268.324873,572.762044 252.48731,548.603641 211.06599,547.139496 C191.573604,545.67535 180,531.765966 180,512 C180,492.234034 191.573604,478.32465 211.06599,476.860504 C252.48731,474.664286 268.324873,451.237956 268.324873,397.064567 C268.324873,391.425318 268.074734,382.32892 267.801145,373.044633 L267.801145,373.044633 L267.687192,369.200715 C267.392429,359.287214 267.106599,349.67429 267.106599,344.355323 C267.106599,273.344259 292.081218,227.955744 386.497462,203.065268 Z M637.502538,203.065268 C731.918782,227.955744 756.893401,273.344259 756.893401,344.355323 C756.893401,349.67429 756.607571,359.287214 756.312808,369.200715 L756.198855,373.044633 C755.925266,382.32892 755.675127,391.425318 755.675127,397.064567 C755.675127,451.237956 771.51269,474.664286 812.93401,476.860504 C832.426396,478.32465 844,492.234034 844,512 C844,531.765966 832.426396,545.67535 812.93401,547.139496 C771.51269,548.603641 755.675127,572.762044 755.675127,626.203361 C755.675127,631.842609 755.925266,640.939007 756.198855,650.223294 L756.312808,654.067212 C756.607571,663.980713 756.893401,673.593637 756.893401,678.912604 C756.893401,750.655741 731.918782,796.044256 637.502538,820.934732 C618.619289,826.059242 604,812.149858 604,790.919746 C604,773.349998 613.746193,763.833052 621.055838,760.90476 C680.142132,738.942576 688.670051,716.248318 688.670051,666.467366 C688.670051,659.479398 688.392541,650.323572 688.102417,640.989618 L688.01941,638.319645 L687.95753,636.317008 C687.691058,627.645952 687.451777,619.107246 687.451777,612.293977 C687.451777,555.192297 709.989848,528.105602 750.80203,512 C710.598985,496.626471 687.451777,468.807703 687.451777,411.706023 C687.451777,396.332494 688.670051,372.174091 688.670051,357.532634 C688.670051,307.751682 680.142132,285.057424 621.055838,263.09524 C613.746193,260.166948 604,250.650002 604,233.080254 C604,211.850142 618.619289,197.940758 637.502538,203.065268 Z M817.502538,203.065268 C911.918782,227.955744 936.893401,273.344259 936.893401,344.355323 C936.893401,349.67429 936.607571,359.287214 936.312808,369.200715 L936.198855,373.044633 C935.925266,382.32892 935.675127,391.425318 935.675127,397.064567 C935.675127,451.237956 951.51269,474.664286 992.93401,476.860504 C1012.4264,478.32465 1024,492.234034 1024,512 C1024,531.765966 1012.4264,545.67535 992.93401,547.139496 C951.51269,548.603641 935.675127,572.762044 935.675127,626.203361 C935.675127,631.842609 935.925266,640.939007 936.198855,650.223294 L936.312808,654.067212 C936.607571,663.980713 936.893401,673.593637 936.893401,678.912604 C936.893401,750.655741 911.918782,796.044256 817.502538,820.934732 C798.619289,826.059242 784,812.149858 784,790.919746 C784,773.349998 793.746193,763.833052 801.055838,760.90476 C860.142132,738.942576 868.670051,716.248318 868.670051,666.467366 C868.670051,659.479398 868.392541,650.323572 868.102417,640.989618 L868.01941,638.319645 L867.95753,636.317008 C867.691058,627.645952 867.451777,619.107246 867.451777,612.293977 C867.451777,555.192297 889.989848,528.105602 930.80203,512 C890.598985,496.626471 867.451777,468.807703 867.451777,411.706023 C867.451777,396.332494 868.670051,372.174091 868.670051,357.532634 C868.670051,307.751682 860.142132,285.057424 801.055838,263.09524 C793.746193,260.166948 784,250.650002 784,233.080254 C784,211.850142 798.619289,197.940758 817.502538,203.065268 Z"></path>
)
