import React from 'react'

export const InputSource = (
  <svg viewBox="0 0 1424 1024">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(150.000000, 512.000000) rotate(90.000000) translate(-150.000000, -512.000000) "
        x="-50"
        y="502"
        width="400"
        height="20"
        rx="10"
      ></rect>
      <path
        d="M1344,218 C1388.18278,218 1424,253.81722 1424,298 L1424,726 C1424,770.18278 1388.18278,806 1344,806 L80,806 C35.81722,806 0,770.18278 0,726 L0,298 C0,253.81722 35.81722,218 80,218 L1344,218 Z M1344,238 L80,238 C47.1942859,238 20.5378857,264.328343 20,297.00779 L20,298 L20,726 C20,758.805714 46.328343,785.462114 79.0077903,785.991962 L80,786 L1344,786 C1376.80571,786 1403.46211,759.671657 1404,726.99221 L1404,726 L1404,298 C1404,265.194286 1377.67166,238.537886 1344.99221,238.008038 L1344,238 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
    </g>
  </svg>
)

export const TextAreaSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M947.487373,862.63456 C951.392616,866.539803 951.392616,872.871452 947.487373,876.776695 L876.776695,947.487373 C872.871452,951.392616 866.539803,951.392616 862.63456,947.487373 C858.729317,943.58213 858.729317,937.250481 862.63456,933.345238 L933.345238,862.63456 C937.250481,858.729317 943.58213,858.729317 947.487373,862.63456 Z M912.132034,784.852814 C916.037277,788.758057 916.037277,795.089706 912.132034,798.994949 L798.994949,912.132034 C795.089706,916.037277 788.758057,916.037277 784.852814,912.132034 C780.947571,908.226791 780.947571,901.895142 784.852814,897.989899 L897.989899,784.852814 C901.895142,780.947571 908.226791,780.947571 912.132034,784.852814 Z M876.776695,707.071068 C880.681938,710.976311 880.681938,717.307961 876.776695,721.213203 L721.213203,876.776695 C717.307961,880.681938 710.976311,880.681938 707.071068,876.776695 C703.165825,872.871452 703.165825,866.539803 707.071068,862.63456 L862.63456,707.071068 C866.539803,703.165825 872.871452,703.165825 876.776695,707.071068 Z M150,90 C155.522847,90 160,94.4771525 160,100 L160,380 C160,385.522847 155.522847,390 150,390 C144.477153,390 140,385.522847 140,380 L140,100 C140,94.4771525 144.477153,90 150,90 Z"
      fill="var(--dn-brand-color)"
    ></path>
  </g>
)

export const SelectSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(512.000000, 571.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -571.000000) "
      x="502"
      y="171"
      width="20"
      height="800"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(512.000000, 791.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -791.000000) "
      x="502"
      y="391"
      width="20"
      height="800"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(513.500000, 350.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -350.000000) "
      x="503.5"
      y="-150.5"
      width="20"
      height="1001"
      rx="10"
    ></rect>
    <path
      d="M903.653115,129.492777 C906.69467,124.882919 912.897366,123.611559 917.507223,126.653115 C922.036206,129.64131 923.342804,135.6807 920.50299,140.263154 L920.346885,140.507223 L863.014644,227.401401 C860.820018,230.727631 857.984696,233.583294 854.674239,235.801642 C841.047887,244.932702 822.647581,241.416301 813.331586,227.989873 L813.052078,227.579981 L754.744243,140.56675 C751.669812,135.978753 752.896799,129.767127 757.484796,126.692696 C761.990865,123.673166 768.063093,124.802804 771.191174,129.190685 L771.35885,129.43325 L829.666685,216.446481 C832.741116,221.034478 838.952742,222.261466 843.540739,219.187035 C844.533876,218.52153 845.398727,217.683951 846.09506,216.714816 L846.320874,216.386954 L903.653115,129.492777 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(362.000000, 181.000000) scale(-1, 1) rotate(630.000000) translate(-362.000000, -181.000000) "
      x="352"
      y="-69"
      width="20"
      height="500"
      rx="10"
    ></rect>
  </g>
)
export const TreeSelectSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(513.500000, 350.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -350.000000) "
      x="503.5"
      y="-150.5"
      width="20"
      height="1001"
      rx="10"
    ></rect>
    <g transform="translate(244.000000, 435.000000)">
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(246.000000, 249.582549) scale(-1, 1) rotate(630.000000) translate(-246.000000, -249.582549) "
        x="236"
        y="131.582549"
        width="20"
        height="236"
        rx="10"
      ></rect>
      <path
        d="M475.900188,188.615819 C509.037273,188.615819 535.900188,215.478734 535.900188,248.615819 L535.900188,250.549278 C535.900188,283.686363 509.037273,310.549278 475.900188,310.549278 L413,310.549278 C379.862915,310.549278 353,283.686363 353,250.549278 L353,248.615819 C353,215.478734 379.862915,188.615819 413,188.615819 L475.900188,188.615819 Z M475.900188,208.615819 L413,208.615819 C391.129524,208.615819 373.35859,226.168048 373.005359,247.954346 L373,248.615819 L373,250.549278 C373,272.419754 390.552229,290.190688 412.338527,290.543919 L413,290.549278 L475.900188,290.549278 C497.770664,290.549278 515.541598,272.997049 515.894829,251.210751 L515.900188,250.549278 L515.900188,248.615819 C515.900188,226.745343 498.34796,208.97441 476.561661,208.621178 L475.900188,208.615819 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <path
        d="M475.900188,371.516008 C509.037273,371.516008 535.900188,398.378923 535.900188,431.516008 L535.900188,433.449466 C535.900188,466.586551 509.037273,493.449466 475.900188,493.449466 L413,493.449466 C379.862915,493.449466 353,466.586551 353,433.449466 L353,431.516008 C353,398.378923 379.862915,371.516008 413,371.516008 L475.900188,371.516008 Z M475.900188,391.516008 L413,391.516008 C391.129524,391.516008 373.35859,409.068236 373.005359,430.854534 L373,431.516008 L373,433.449466 C373,455.319943 390.552229,473.090876 412.338527,473.444108 L413,473.449466 L475.900188,473.449466 C497.770664,473.449466 515.541598,455.897238 515.894829,434.11094 L515.900188,433.449466 L515.900188,431.516008 C515.900188,409.645531 498.34796,391.874598 476.561661,391.521366 L475.900188,391.516008 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <path
        d="M147,141.5 L147,372.5 C147,399.838095 168.940286,422.051762 196.173159,422.493301 L197,422.5 L364,422.5 L364,442.5 L197,442.5 C158.726667,442.5 127.627533,411.7836 127.009378,373.657578 L127,372.5 L127,141.5 L147,141.5 Z"
        fill="var(--dn-brand-color)"
        fillRule="nonzero"
      ></path>
      <path
        d="M214.350282,1.56319402e-13 C247.487367,1.56319402e-13 274.350282,26.862915 274.350282,60 L274.350282,92.4168236 C274.350282,125.553909 247.487367,152.416824 214.350282,152.416824 L60,152.416824 C26.862915,152.416824 2.84217094e-14,125.553909 2.84217094e-14,92.4168236 L2.84217094e-14,60 C2.84217094e-14,26.862915 26.862915,1.56319402e-13 60,1.56319402e-13 L214.350282,1.56319402e-13 Z M214.350282,20 L60,20 C38.1295239,20 20.3585905,37.5522287 20.0053589,59.3385269 L20,60 L20,92.4168236 C20,114.2873 37.5522287,132.058233 59.3385269,132.411465 L60,132.416824 L214.350282,132.416824 C236.220759,132.416824 253.991692,114.864595 254.344924,93.0782967 L254.350282,92.4168236 L254.350282,60 C254.350282,38.1295239 236.798054,20.3585905 215.011756,20.0053589 L214.350282,20 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
    </g>
    <path
      d="M903.653115,129.492777 C906.69467,124.882919 912.897366,123.611559 917.507223,126.653115 C922.036206,129.64131 923.342804,135.6807 920.50299,140.263154 L920.346885,140.507223 L863.014644,227.401401 C860.820018,230.727631 857.984696,233.583294 854.674239,235.801642 C841.047887,244.932702 822.647581,241.416301 813.331586,227.989873 L813.052078,227.579981 L754.744243,140.56675 C751.669812,135.978753 752.896799,129.767127 757.484796,126.692696 C761.990865,123.673166 768.063093,124.802804 771.191174,129.190685 L771.35885,129.43325 L829.666685,216.446481 C832.741116,221.034478 838.952742,222.261466 843.540739,219.187035 C844.533876,218.52153 845.398727,217.683951 846.09506,216.714816 L846.320874,216.386954 L903.653115,129.492777 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(362.000000, 181.000000) scale(-1, 1) rotate(630.000000) translate(-362.000000, -181.000000) "
      x="352"
      y="-69"
      width="20"
      height="500"
      rx="10"
    ></rect>
  </g>
)

export const CascaderSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(513.500000, 350.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -350.000000) "
      x="503.5"
      y="-150.5"
      width="20"
      height="1001"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(512.000000, 571.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -571.000000) "
      x="502"
      y="171"
      width="20"
      height="800"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(512.000000, 791.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -791.000000) "
      x="502"
      y="391"
      width="20"
      height="800"
      rx="10"
    ></rect>
    <path
      d="M903.653115,129.492777 C906.69467,124.882919 912.897366,123.611559 917.507223,126.653115 C922.036206,129.64131 923.342804,135.6807 920.50299,140.263154 L920.346885,140.507223 L863.014644,227.401401 C860.820018,230.727631 857.984696,233.583294 854.674239,235.801642 C841.047887,244.932702 822.647581,241.416301 813.331586,227.989873 L813.052078,227.579981 L754.744243,140.56675 C751.669812,135.978753 752.896799,129.767127 757.484796,126.692696 C761.990865,123.673166 768.063093,124.802804 771.191174,129.190685 L771.35885,129.43325 L829.666685,216.446481 C832.741116,221.034478 838.952742,222.261466 843.540739,219.187035 C844.533876,218.52153 845.398727,217.683951 846.09506,216.714816 L846.320874,216.386954 L903.653115,129.492777 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M189.695312,247.351563 C194.03125,247.351563 196.199219,245.535156 196.199219,241.902344 C196.199219,241.667969 195.613281,239.265625 194.441406,234.695313 L194.441406,234.695313 L163.503906,118.328125 C162.566406,114.695313 160.457031,112.878906 157.175781,112.878906 C153.777344,112.878906 151.609375,114.695313 150.671875,118.328125 L150.671875,118.328125 L119.382812,235.398438 C119.28583,235.774246 119.193863,236.134378 119.106913,236.478834 L118.939702,237.146844 C118.297609,239.735281 117.976562,241.320447 117.976562,241.902344 C117.976562,245.535156 119.910156,247.351563 123.777344,247.351563 C127.292969,247.351563 129.519531,245.535156 130.457031,241.902344 L130.457031,241.902344 L139.070312,209.558594 L174.929688,209.558594 L183.71875,241.902344 C184.65625,245.535156 186.648438,247.351563 189.695312,247.351563 Z M171.765625,196.726563 L142.410156,196.726563 L157.175781,140.300781 L171.765625,196.726563 Z M315.183594,244.1875 C317.996094,244.1875 322.976562,244.070313 330.125,243.835938 C337.039062,243.71875 342.605469,243.660156 346.824219,243.660156 C357.605469,243.660156 366.804688,240.554688 374.421875,234.34375 C382.507812,227.78125 386.550781,219.226563 386.550781,208.679688 C386.550781,200.945313 383.914062,194.265625 378.640625,188.640625 C373.953125,183.601563 367.742188,179.910156 360.007812,177.566406 C375.476562,171.003906 383.210938,160.984375 383.210938,147.507813 C383.210938,137.078125 379.226562,128.992188 371.257812,123.25 C364.226562,117.976563 355.261719,115.339844 344.363281,115.339844 L344.363281,115.339844 L315.183594,115.339844 C310.027344,115.339844 307.449219,117.917969 307.449219,123.074219 L307.449219,123.074219 L307.449219,236.453125 C307.449219,241.609375 310.027344,244.1875 315.183594,244.1875 Z M320.316345,171.765625 L320.105469,171.765625 L320.105469,133.09375 C320.105469,129.8125 321.804688,128.171875 325.203125,128.171875 L325.203125,128.171875 L345.066406,128.171875 C351.628906,128.171875 357.136719,129.695313 361.589844,132.742188 C366.746094,136.375 369.324219,141.296875 369.324219,147.507813 C369.324219,155.007813 365.691406,161.101563 358.425781,165.789063 C351.980469,169.65625 344.890625,171.589844 337.15625,171.589844 L337.15625,171.589844 L328.367188,171.589844 C324.5,171.707031 321.746094,171.765625 320.105469,171.765625 L320.316345,171.765625 Z M325.203125,231.53125 C321.804688,231.53125 320.105469,229.832031 320.105469,226.433594 L320.105469,226.433594 L320.105469,184.773438 L332.761719,184.773438 C342.488281,184.773438 350.925781,186.355469 358.074219,189.519531 C367.917969,193.738281 372.839844,200.125 372.839844,208.679688 C372.839844,215.828125 369.910156,221.453125 364.050781,225.554688 C358.894531,229.304688 352.507812,231.179688 344.890625,231.179688 C342.429688,231.179688 339.03125,231.238281 334.695312,231.355469 C330.476562,231.472656 327.3125,231.53125 325.203125,231.53125 Z M538.40625,247.175781 C552.234375,247.175781 563.074219,240.4375 570.925781,226.960938 C574.910156,219.929688 576.902344,214.773438 576.902344,211.492188 C576.902344,206.921875 574.441406,204.636719 569.519531,204.636719 C567.292969,204.636719 565.710938,206.570313 564.773438,210.4375 C562.898438,217.351563 559.851562,222.917969 555.632812,227.136719 C550.945312,231.941406 545.203125,234.34375 538.40625,234.34375 C528.445312,234.34375 521.003906,227.605469 516.082031,214.128906 C512.683594,204.519531 510.984375,193.796875 510.984375,181.960938 C510.984375,168.484375 512.507812,156.882813 515.554688,147.15625 C520.242188,132.625 527.859375,125.359375 538.40625,125.359375 C545.90625,125.359375 551.648438,127.820313 555.632812,132.742188 C557.859375,135.4375 560.203125,140.125 562.664062,146.804688 C564.539062,151.726563 566.355469,154.1875 568.113281,154.1875 C573.035156,154.1875 575.496094,151.902344 575.496094,147.332031 C575.496094,137.957031 571.6875,129.871094 564.070312,123.074219 C556.335938,116.277344 547.78125,112.878906 538.40625,112.878906 C523.40625,112.878906 512.332031,121.199219 505.183594,137.839844 C499.910156,150.144531 497.273438,164.851563 497.273438,181.960938 C497.273438,197.78125 500.144531,211.726563 505.886719,223.796875 C513.386719,239.382813 524.226562,247.175781 538.40625,247.175781 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(248.000000, 183.602540) scale(-1, 1) rotate(75.000000) translate(-248.000000, -183.602540) "
      x="188"
      y="173.60254"
      width="120"
      height="20"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(449.000000, 183.602540) scale(-1, 1) rotate(75.000000) translate(-449.000000, -183.602540) "
      x="389"
      y="173.60254"
      width="120"
      height="20"
      rx="10"
    ></rect>
  </g>
)

export const RadioGroupSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M182,172 C259.319865,172 322,234.680135 322,312 C322,389.319865 259.319865,452 182,452 C104.680135,452 42,389.319865 42,312 C42,234.680135 104.680135,172 182,172 Z M182,192 C115.72583,192 62,245.72583 62,312 C62,378.27417 115.72583,432 182,432 C248.27417,432 302,378.27417 302,312 C302,245.72583 248.27417,192 182,192 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(702.000000, 312.000000) scale(-1, 1) rotate(630.000000) translate(-702.000000, -312.000000) "
      x="692"
      y="32"
      width="20"
      height="560"
      rx="10"
    ></rect>
    <path
      d="M182,252 C215.137085,252 242,278.862915 242,312 C242,345.137085 215.137085,372 182,372 C148.862915,372 122,345.137085 122,312 C122,278.862915 148.862915,252 182,252 Z M182,272 C159.90861,272 142,289.90861 142,312 C142,334.09139 159.90861,352 182,352 C204.09139,352 222,334.09139 222,312 C222,289.90861 204.09139,272 182,272 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M182,572 C259.319865,572 322,634.680135 322,712 C322,789.319865 259.319865,852 182,852 C104.680135,852 42,789.319865 42,712 C42,634.680135 104.680135,572 182,572 Z M182,592 C115.72583,592 62,645.72583 62,712 C62,778.27417 115.72583,832 182,832 C248.27417,832 302,778.27417 302,712 C302,645.72583 248.27417,592 182,592 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(702.000000, 712.000000) scale(-1, 1) rotate(630.000000) translate(-702.000000, -712.000000) "
      x="692"
      y="432"
      width="20"
      height="560"
      rx="10"
    ></rect>
    <path
      d="M182,172 C259.319865,172 322,234.680135 322,312 C322,389.319865 259.319865,452 182,452 C104.680135,452 42,389.319865 42,312 C42,234.680135 104.680135,172 182,172 Z M182,192 C115.72583,192 62,245.72583 62,312 C62,378.27417 115.72583,432 182,432 C248.27417,432 302,378.27417 302,312 C302,245.72583 248.27417,192 182,192 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(702.000000, 312.000000) scale(-1, 1) rotate(630.000000) translate(-702.000000, -312.000000) "
      x="692"
      y="32"
      width="20"
      height="560"
      rx="10"
    ></rect>
    <path
      d="M182,252 C215.137085,252 242,278.862915 242,312 C242,345.137085 215.137085,372 182,372 C148.862915,372 122,345.137085 122,312 C122,278.862915 148.862915,252 182,252 Z M182,272 C159.90861,272 142,289.90861 142,312 C142,334.09139 159.90861,352 182,352 C204.09139,352 222,334.09139 222,312 C222,289.90861 204.09139,272 182,272 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M182,572 C259.319865,572 322,634.680135 322,712 C322,789.319865 259.319865,852 182,852 C104.680135,852 42,789.319865 42,712 C42,634.680135 104.680135,572 182,572 Z M182,592 C115.72583,592 62,645.72583 62,712 C62,778.27417 115.72583,832 182,832 C248.27417,832 302,778.27417 302,712 C302,645.72583 248.27417,592 182,592 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(702.000000, 712.000000) scale(-1, 1) rotate(630.000000) translate(-702.000000, -712.000000) "
      x="692"
      y="432"
      width="20"
      height="560"
      rx="10"
    ></rect>
  </g>
)

export const CheckboxGroupSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M282,162 L82,162 C54.3857625,162 32,184.385763 32,212 L32,412 C32,439.614237 54.3857625,462 82,462 L282,462 C309.614237,462 332,439.614237 332,412 L332,212 C332,184.385763 309.614237,162 282,162 Z M82,182 L282,182 C298.568542,182 312,195.431458 312,212 L312,412 C312,428.568542 298.568542,442 282,442 L82,442 C65.4314575,442 52,428.568542 52,412 L52,212 C52,195.431458 65.4314575,182 82,182 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M282,562 L82,562 C54.3857625,562 32,584.385763 32,612 L32,812 C32,839.614237 54.3857625,862 82,862 L282,862 C309.614237,862 332,839.614237 332,812 L332,612 C332,584.385763 309.614237,562 282,562 Z M82,582 L282,582 C298.568542,582 312,595.431458 312,612 L312,812 C312,828.568542 298.568542,842 282,842 L82,842 C65.4314575,842 52,828.568542 52,812 L52,612 C52,595.431458 65.4314575,582 82,582 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(702.000000, 312.000000) scale(-1, 1) rotate(630.000000) translate(-702.000000, -312.000000) "
      x="692"
      y="32"
      width="20"
      height="560"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(702.000000, 712.000000) scale(-1, 1) rotate(630.000000) translate(-702.000000, -712.000000) "
      x="692"
      y="432"
      width="20"
      height="560"
      rx="10"
    ></rect>
    <path
      d="M266.959609,247.398388 C270.881721,243.510087 277.213311,243.537497 281.101612,247.459609 C284.919216,251.31041 284.962205,257.483868 281.250462,261.387129 L281.040391,261.601612 L179.182262,362.581653 C167.569231,374.094573 148.905532,374.169556 137.200441,362.836926 L136.847885,362.490021 L82.9289322,308.571068 C79.0236893,304.665825 79.0236893,298.334175 82.9289322,294.428932 C86.7631707,290.594694 92.9363852,290.52498 96.8556774,294.219792 L97.0710678,294.428932 L150.990021,348.347885 C154.812528,352.170392 160.962783,352.253054 164.885852,348.586012 L165.10148,348.37843 L266.959609,247.398388 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
  </g>
)

export const SliderSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <rect fill="#999999" x="289" y="304" width="735" height="20" rx="10"></rect>
    <rect fill="#999999" x="0" y="702" width="531" height="20" rx="10"></rect>
    <rect fill="#999999" x="799" y="702" width="225" height="20" rx="10"></rect>
    <path
      d="M150,164 C67.1572875,164 0,231.157288 0,314 C0,396.842712 67.1572875,464 150,464 C232.842712,464 300,396.842712 300,314 C300,231.157288 232.842712,164 150,164 Z M150,184 C221.797017,184 280,242.202983 280,314 C280,385.797017 221.797017,444 150,444 C78.2029825,444 20,385.797017 20,314 C20,242.202983 78.2029825,184 150,184 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M664,562 C581.157288,562 514,629.157288 514,712 C514,794.842712 581.157288,862 664,862 C746.842712,862 814,794.842712 814,712 C814,629.157288 746.842712,562 664,562 Z M664,582 C735.797017,582 794,640.202983 794,712 C794,783.797017 735.797017,842 664,842 C592.202983,842 534,783.797017 534,712 C534,640.202983 592.202983,582 664,582 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
  </g>
)

export const RateSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M487.286155,34.2736908 L320.815283,276.187129 C319.518769,278.071208 317.615604,279.453938 315.423095,280.104784 L33.9074136,363.672628 C30.3952048,364.715226 27.103676,366.392344 24.1957719,368.620957 C11.0451673,378.699558 8.55481647,397.53055 18.6334176,410.681154 L197.264441,643.759726 C198.655661,645.574996 199.382605,647.8123 199.324074,650.098623 L191.808687,943.65983 C191.714925,947.32232 192.29282,950.971008 193.513765,954.425268 C199.035324,970.046695 216.175101,978.234256 231.796528,972.712697 L508.667444,874.849738 C510.82378,874.087558 513.17622,874.087558 515.332556,874.849738 L792.203472,972.712697 C795.657733,973.933642 799.30642,974.511537 802.96891,974.417775 C819.532026,973.993747 832.615341,960.222946 832.191313,943.65983 L824.675926,650.098623 C824.617395,647.8123 825.344339,645.574996 826.735559,643.759726 L1005.36658,410.681154 C1007.5952,407.77325 1009.27231,404.481721 1010.31491,400.969512 C1015.02992,385.086021 1005.97608,368.387637 990.092586,363.672628 L708.576905,280.104784 C706.384396,279.453938 704.481231,278.071208 703.184717,276.187129 L536.713845,34.2736908 C534.636943,31.2555623 532.024771,28.6433902 529.006642,26.5664883 C515.357563,17.1739791 496.678664,20.6246113 487.286155,34.2736908 Z M517.668881,43.0423848 C518.674924,43.7346854 519.545648,44.6054094 520.237948,45.6114523 L686.708821,287.524891 C690.598363,293.177126 696.307856,297.325316 702.885386,299.277853 L984.401066,382.845698 C989.695563,384.417367 992.713511,389.983495 991.141841,395.277992 C990.794309,396.448729 990.235269,397.545905 989.492398,398.515206 L810.861375,631.593778 C806.687715,637.039589 804.506882,643.751501 804.682477,650.610471 L812.197864,944.171677 C812.339206,949.692716 807.978101,954.282983 802.457063,954.430326 C801.236233,954.45558 800.020004,954.262948 798.868583,953.855966 L521.997667,855.993007 C515.528661,853.706468 508.471339,853.706468 502.002333,855.993007 L225.131417,953.855966 C219.924274,955.696486 214.211015,952.967299 212.370496,947.760157 C211.963514,946.608737 211.770882,945.392507 211.802136,944.171677 L219.317523,650.610471 C219.493118,643.751501 217.312285,637.039589 213.138625,631.593778 L34.5076015,398.515206 C31.1480678,394.131671 31.9781848,387.854674 36.3617196,384.49514 C37.331021,383.75227 38.4281973,383.19323 39.5989335,382.845698 L321.114614,299.277853 C327.692144,297.325316 333.401637,293.177126 337.291179,287.524891 L503.762052,45.6114523 C506.83698,41.1430036 512.897807,39.9537114 517.42357,42.8787518 L517.668881,43.0423848 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(512.000000, 659.000000) scale(-1, 1) rotate(90.000000) translate(-512.000000, -659.000000) "
      x="502"
      y="479"
      width="20"
      height="360"
      rx="10"
    ></rect>
  </g>
)
export const DatePickerSource = {
  light: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <rect
        fill="#999999"
        transform="translate(266.000000, 123.000000) scale(-1, 1) translate(-266.000000, -123.000000) "
        x="256"
        y="63"
        width="20"
        height="120"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(770.000000, 123.000000) scale(-1, 1) translate(-770.000000, -123.000000) "
        x="760"
        y="63"
        width="20"
        height="120"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(512.000000, 310.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -310.000000) "
        x="502"
        y="-202"
        width="20"
        height="1024"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="152"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="152"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="152"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="632"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="632"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="632"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="312"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="312"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="312"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="792"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="792"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="792"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <path
        d="M165.894531,113.494448 C171.417379,113.494448 175.894531,117.9716 175.894531,123.494448 C175.894531,128.923687 171.56786,133.342379 166.174436,133.490606 L165.894531,133.494448 L88.4375,133.494448 C51.0375365,133.494448 20.613907,163.802329 20.0091744,201.462605 L20,202.606052 L20,934.388395 C20,972.194862 50.0496299,1002.88081 87.3063267,1003.49075 L88.4375,1003.5 L935.5625,1003.5 C972.962464,1003.5 1003.38609,973.192119 1003.99083,935.531843 L1004,934.388395 L1004,202.606052 C1004,164.799585 973.95037,134.113639 936.693673,133.503701 L935.5625,133.494448 L859.085938,133.494448 C853.56309,133.494448 849.085938,129.017295 849.085938,123.494448 C849.085938,118.065208 853.412608,113.646516 858.806033,113.494448 L859.085938,113.494448 L935.5625,113.494448 C983.93581,113.494448 1023.20756,152.615815 1023.98816,201.132989 L1024,202.606052 L1024,934.388395 C1024,983.092351 985.211983,1022.70076 937.025542,1023.48806 L935.5625,1023.5 L88.4375,1023.5 C40.0641901,1023.5 0.792440052,984.378633 0.011842382,935.861458 L0,934.388395 L0,202.606052 C0,153.902096 38.788017,114.293689 86.9744583,113.506392 L88.4375,113.494448 L165.894531,113.494448 Z M661.03125,113.494448 C666.554097,113.494448 671.03125,117.9716 671.03125,123.494448 C671.03125,128.923687 666.704579,133.342379 661.311155,133.490606 L661.03125,133.494448 L363.949219,133.494448 C358.426371,133.494448 353.949219,129.017295 353.949219,123.494448 C353.949219,118.065208 358.27589,113.646516 363.669314,113.498289 L363.949219,113.494448 L661.03125,113.494448 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="#D8D8D8"
        x="472"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="472"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="472"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
    </g>
  ),
  dark: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <rect
        fill="#999999"
        transform="translate(266.000000, 123.000000) scale(-1, 1) translate(-266.000000, -123.000000) "
        x="256"
        y="63"
        width="20"
        height="120"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(770.000000, 123.000000) scale(-1, 1) translate(-770.000000, -123.000000) "
        x="760"
        y="63"
        width="20"
        height="120"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(512.000000, 310.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -310.000000) "
        x="502"
        y="-202"
        width="20"
        height="1024"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="152"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="152"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="152"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="632"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="632"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="632"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="312"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="312"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="312"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="792"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="792"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="792"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <path
        d="M165.894531,113.494448 C171.417379,113.494448 175.894531,117.9716 175.894531,123.494448 C175.894531,128.923687 171.56786,133.342379 166.174436,133.490606 L165.894531,133.494448 L88.4375,133.494448 C51.0375365,133.494448 20.613907,163.802329 20.0091744,201.462605 L20,202.606052 L20,934.388395 C20,972.194862 50.0496299,1002.88081 87.3063267,1003.49075 L88.4375,1003.5 L935.5625,1003.5 C972.962464,1003.5 1003.38609,973.192119 1003.99083,935.531843 L1004,934.388395 L1004,202.606052 C1004,164.799585 973.95037,134.113639 936.693673,133.503701 L935.5625,133.494448 L859.085938,133.494448 C853.56309,133.494448 849.085938,129.017295 849.085938,123.494448 C849.085938,118.065208 853.412608,113.646516 858.806033,113.494448 L859.085938,113.494448 L935.5625,113.494448 C983.93581,113.494448 1023.20756,152.615815 1023.98816,201.132989 L1024,202.606052 L1024,934.388395 C1024,983.092351 985.211983,1022.70076 937.025542,1023.48806 L935.5625,1023.5 L88.4375,1023.5 C40.0641901,1023.5 0.792440052,984.378633 0.011842382,935.861458 L0,934.388395 L0,202.606052 C0,153.902096 38.788017,114.293689 86.9744583,113.506392 L88.4375,113.494448 L165.894531,113.494448 Z M661.03125,113.494448 C666.554097,113.494448 671.03125,117.9716 671.03125,123.494448 C671.03125,128.923687 666.704579,133.342379 661.311155,133.490606 L661.03125,133.494448 L363.949219,133.494448 C358.426371,133.494448 353.949219,129.017295 353.949219,123.494448 C353.949219,118.065208 358.27589,113.646516 363.669314,113.498289 L363.949219,113.494448 L661.03125,113.494448 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="#4D4D4D"
        x="472"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="472"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="472"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
    </g>
  ),
}

export const DateRangePickerSource = {
  light: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <rect
        fill="#999999"
        transform="translate(266.000000, 123.000000) scale(-1, 1) translate(-266.000000, -123.000000) "
        x="256"
        y="63"
        width="20"
        height="120"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(770.000000, 123.000000) scale(-1, 1) translate(-770.000000, -123.000000) "
        x="760"
        y="63"
        width="20"
        height="120"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(512.000000, 310.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -310.000000) "
        x="502"
        y="-202"
        width="20"
        height="1024"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="152"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="152"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="152"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="632"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="632"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="632"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="312"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="312"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="312"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="792"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="792"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="792"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="472"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="472"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#D8D8D8"
        x="472"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <path
        d="M165.894531,113.494448 C171.417379,113.494448 175.894531,117.9716 175.894531,123.494448 C175.894531,128.923687 171.56786,133.342379 166.174436,133.490606 L165.894531,133.494448 L88.4375,133.494448 C51.0375365,133.494448 20.613907,163.802329 20.0091744,201.462605 L20,202.606052 L20,934.388395 C20,972.194862 50.0496299,1002.88081 87.3063267,1003.49075 L88.4375,1003.5 L935.5625,1003.5 C972.962464,1003.5 1003.38609,973.192119 1003.99083,935.531843 L1004,934.388395 L1004,202.606052 C1004,164.799585 973.95037,134.113639 936.693673,133.503701 L935.5625,133.494448 L859.085938,133.494448 C853.56309,133.494448 849.085938,129.017295 849.085938,123.494448 C849.085938,118.065208 853.412608,113.646516 858.806033,113.494448 L859.085938,113.494448 L935.5625,113.494448 C983.93581,113.494448 1023.20756,152.615815 1023.98816,201.132989 L1024,202.606052 L1024,934.388395 C1024,983.092351 985.211983,1022.70076 937.025542,1023.48806 L935.5625,1023.5 L88.4375,1023.5 C40.0641901,1023.5 0.792440052,984.378633 0.011842382,935.861458 L0,934.388395 L0,202.606052 C0,153.902096 38.788017,114.293689 86.9744583,113.506392 L88.4375,113.494448 L165.894531,113.494448 Z M661.03125,113.494448 C666.554097,113.494448 671.03125,117.9716 671.03125,123.494448 C671.03125,128.923687 666.704579,133.342379 661.311155,133.490606 L661.03125,133.494448 L363.949219,133.494448 C358.426371,133.494448 353.949219,129.017295 353.949219,123.494448 C353.949219,118.065208 358.27589,113.646516 363.669314,113.498289 L363.949219,113.494448 L661.03125,113.494448 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
    </g>
  ),
  dark: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <rect
        fill="#999999"
        transform="translate(266.000000, 123.000000) scale(-1, 1) translate(-266.000000, -123.000000) "
        x="256"
        y="63"
        width="20"
        height="120"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(770.000000, 123.000000) scale(-1, 1) translate(-770.000000, -123.000000) "
        x="760"
        y="63"
        width="20"
        height="120"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(512.000000, 310.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -310.000000) "
        x="502"
        y="-202"
        width="20"
        height="1024"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="152"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="152"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="152"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="632"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="632"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="632"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="312"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="312"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="312"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="792"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="792"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="792"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="472"
        y="457"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        x="472"
        y="617"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <rect
        fill="#4D4D4D"
        x="472"
        y="777"
        width="80"
        height="80"
        rx="10"
      ></rect>
      <path
        d="M165.894531,113.494448 C171.417379,113.494448 175.894531,117.9716 175.894531,123.494448 C175.894531,128.923687 171.56786,133.342379 166.174436,133.490606 L165.894531,133.494448 L88.4375,133.494448 C51.0375365,133.494448 20.613907,163.802329 20.0091744,201.462605 L20,202.606052 L20,934.388395 C20,972.194862 50.0496299,1002.88081 87.3063267,1003.49075 L88.4375,1003.5 L935.5625,1003.5 C972.962464,1003.5 1003.38609,973.192119 1003.99083,935.531843 L1004,934.388395 L1004,202.606052 C1004,164.799585 973.95037,134.113639 936.693673,133.503701 L935.5625,133.494448 L859.085938,133.494448 C853.56309,133.494448 849.085938,129.017295 849.085938,123.494448 C849.085938,118.065208 853.412608,113.646516 858.806033,113.494448 L859.085938,113.494448 L935.5625,113.494448 C983.93581,113.494448 1023.20756,152.615815 1023.98816,201.132989 L1024,202.606052 L1024,934.388395 C1024,983.092351 985.211983,1022.70076 937.025542,1023.48806 L935.5625,1023.5 L88.4375,1023.5 C40.0641901,1023.5 0.792440052,984.378633 0.011842382,935.861458 L0,934.388395 L0,202.606052 C0,153.902096 38.788017,114.293689 86.9744583,113.506392 L88.4375,113.494448 L165.894531,113.494448 Z M661.03125,113.494448 C666.554097,113.494448 671.03125,117.9716 671.03125,123.494448 C671.03125,128.923687 666.704579,133.342379 661.311155,133.490606 L661.03125,133.494448 L363.949219,133.494448 C358.426371,133.494448 353.949219,129.017295 353.949219,123.494448 C353.949219,118.065208 358.27589,113.646516 363.669314,113.498289 L363.949219,113.494448 L661.03125,113.494448 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
    </g>
  ),
}

export const TimePickerSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M692,67.4415588 C697.522847,67.4415588 702,71.9187113 702,77.4415588 C702,82.8707987 697.673329,87.2894905 692.279905,87.437717 L692,87.4415588 L352,87.4415588 C346.57076,87.4415588 342.152068,91.7682296 342.003842,97.1616539 L342,97.4415588 L342,437.441559 C342,442.964406 337.522847,447.441559 332,447.441559 C326.57076,447.441559 322.152068,443.114888 322.003842,437.721464 L322,437.441559 L322,97.4415588 C322,81.0387017 335.164172,67.7105016 351.503895,67.4455779 L352,67.4415588 L692,67.4415588 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(512.000000, 257.441559) rotate(45.000000) translate(-512.000000, -257.441559) "
    ></path>
    <path
      d="M692,579.441559 C697.522847,579.441559 702,583.918711 702,589.441559 C702,594.870799 697.673329,599.28949 692.279905,599.437717 L692,599.441559 L352,599.441559 C346.57076,599.441559 342.152068,603.76823 342.003842,609.161654 L342,609.441559 L342,949.441559 C342,954.964406 337.522847,959.441559 332,959.441559 C326.57076,959.441559 322.152068,955.114888 322.003842,949.721464 L322,949.441559 L322,609.441559 C322,593.038702 335.164172,579.710502 351.503895,579.445578 L352,579.441559 L692,579.441559 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(512.000000, 769.441559) scale(1, -1) rotate(45.000000) translate(-512.000000, -769.441559) "
    ></path>
    <path
      d="M160,656.390625 C181.354167,656.390625 199.583333,647.927083 214.6875,631 C232.395833,611.208333 241.25,575.921875 241.25,525.140625 C241.25,468.630208 233.567708,432.5625 218.203125,416.9375 C203.098958,401.572917 183.697917,393.890625 160,393.890625 C127.1875,393.890625 104.401042,408.864583 91.640625,438.8125 C83.046875,458.604167 78.75,487.380208 78.75,525.140625 C78.75,575.401042 86.171875,609.776042 101.015625,628.265625 C116.119792,647.015625 135.78125,656.390625 160,656.390625 Z M160,629.828125 C137.604167,629.828125 122.239583,617.328125 113.90625,592.328125 C108.958333,576.703125 106.484375,554.307292 106.484375,525.140625 C106.484375,495.192708 108.958333,472.666667 113.90625,457.5625 C122.239583,432.5625 137.604167,420.0625 160,420.0625 C175.625,420.0625 188.385417,426.052083 198.28125,438.03125 C208.177083,449.75 213.125,478.786458 213.125,525.140625 C213.125,564.463542 206.744792,592.979167 193.984375,610.6875 C184.348958,623.447917 173.020833,629.828125 160,629.828125 Z M360,656.390625 C381.354167,656.390625 399.583333,647.927083 414.6875,631 C432.395833,611.208333 441.25,575.921875 441.25,525.140625 C441.25,468.630208 433.567708,432.5625 418.203125,416.9375 C403.098958,401.572917 383.697917,393.890625 360,393.890625 C327.1875,393.890625 304.401042,408.864583 291.640625,438.8125 C283.046875,458.604167 278.75,487.380208 278.75,525.140625 C278.75,575.401042 286.171875,609.776042 301.015625,628.265625 C316.119792,647.015625 335.78125,656.390625 360,656.390625 Z M360,629.828125 C337.604167,629.828125 322.239583,617.328125 313.90625,592.328125 C308.958333,576.703125 306.484375,554.307292 306.484375,525.140625 C306.484375,495.192708 308.958333,472.666667 313.90625,457.5625 C322.239583,432.5625 337.604167,420.0625 360,420.0625 C375.625,420.0625 388.385417,426.052083 398.28125,438.03125 C408.177083,449.75 413.125,478.786458 413.125,525.140625 C413.125,564.463542 406.744792,592.979167 393.984375,610.6875 C384.348958,623.447917 373.020833,629.828125 360,629.828125 Z"
      stroke="#FFFFFF"
      strokeWidth="0.5"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M664,656.390625 C685.354167,656.390625 703.583333,647.927083 718.6875,631 C736.395833,611.208333 745.25,575.921875 745.25,525.140625 C745.25,468.630208 737.567708,432.5625 722.203125,416.9375 C707.098958,401.572917 687.697917,393.890625 664,393.890625 C631.1875,393.890625 608.401042,408.864583 595.640625,438.8125 C587.046875,458.604167 582.75,487.380208 582.75,525.140625 C582.75,575.401042 590.171875,609.776042 605.015625,628.265625 C620.119792,647.015625 639.78125,656.390625 664,656.390625 Z M664,629.828125 C641.604167,629.828125 626.239583,617.328125 617.90625,592.328125 C612.958333,576.703125 610.484375,554.307292 610.484375,525.140625 C610.484375,495.192708 612.958333,472.666667 617.90625,457.5625 C626.239583,432.5625 641.604167,420.0625 664,420.0625 C679.625,420.0625 692.385417,426.052083 702.28125,438.03125 C712.177083,449.75 717.125,478.786458 717.125,525.140625 C717.125,564.463542 710.744792,592.979167 697.984375,610.6875 C688.348958,623.447917 677.020833,629.828125 664,629.828125 Z M864,656.390625 C885.354167,656.390625 903.583333,647.927083 918.6875,631 C936.395833,611.208333 945.25,575.921875 945.25,525.140625 C945.25,468.630208 937.567708,432.5625 922.203125,416.9375 C907.098958,401.572917 887.697917,393.890625 864,393.890625 C831.1875,393.890625 808.401042,408.864583 795.640625,438.8125 C787.046875,458.604167 782.75,487.380208 782.75,525.140625 C782.75,575.401042 790.171875,609.776042 805.015625,628.265625 C820.119792,647.015625 839.78125,656.390625 864,656.390625 Z M864,629.828125 C841.604167,629.828125 826.239583,617.328125 817.90625,592.328125 C812.958333,576.703125 810.484375,554.307292 810.484375,525.140625 C810.484375,495.192708 812.958333,472.666667 817.90625,457.5625 C826.239583,432.5625 841.604167,420.0625 864,420.0625 C879.625,420.0625 892.385417,426.052083 902.28125,438.03125 C912.177083,449.75 917.125,478.786458 917.125,525.140625 C917.125,564.463542 910.744792,592.979167 897.984375,610.6875 C888.348958,623.447917 877.020833,629.828125 864,629.828125 Z"
      stroke="#FFFFFF"
      strokeWidth="0.5"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <circle fill="#999999" cx="512" cy="452" r="20"></circle>
    <circle fill="#999999" cx="512" cy="572" r="20"></circle>
  </g>
)

export const TimeRangePickerSource = (
  <svg viewBox="0 0 1424 1024">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <path
        d="M495.646406,161.623203 C501.169254,161.623203 505.646406,166.100356 505.646406,171.623203 C505.646406,177.052443 501.319736,181.471135 495.926311,181.619361 L495.646406,181.623203 L208.446406,181.623203 C203.017166,181.623203 198.598475,185.949874 198.450248,191.343298 L198.446406,191.623203 L198.446406,478.823203 C198.446406,484.346051 193.969254,488.823203 188.446406,488.823203 C183.017166,488.823203 178.598475,484.496532 178.450248,479.103108 L178.446406,478.823203 L178.446406,191.623203 C178.446406,175.220346 191.610578,161.892146 207.950302,161.627222 L208.446406,161.623203 L495.646406,161.623203 Z"
        fill="var(--dn-brand-color)"
        fillRule="nonzero"
        transform="translate(342.046406, 325.223203) rotate(45.000000) translate(-342.046406, -325.223203) "
      ></path>
      <path
        d="M495.646406,535.623203 C501.169254,535.623203 505.646406,540.100356 505.646406,545.623203 C505.646406,551.052443 501.319736,555.471135 495.926311,555.619361 L495.646406,555.623203 L208.446406,555.623203 C203.017166,555.623203 198.598475,559.949874 198.450248,565.343298 L198.446406,565.623203 L198.446406,852.823203 C198.446406,858.346051 193.969254,862.823203 188.446406,862.823203 C183.017166,862.823203 178.598475,858.496532 178.450248,853.103108 L178.446406,852.823203 L178.446406,565.623203 C178.446406,549.220346 191.610578,535.892146 207.950302,535.627222 L208.446406,535.623203 L495.646406,535.623203 Z"
        fill="var(--dn-brand-color)"
        fillRule="nonzero"
        transform="translate(342.046406, 699.223203) scale(1, -1) rotate(45.000000) translate(-342.046406, -699.223203) "
      ></path>
      <path
        d="M96,613.273438 C110.947917,613.273438 123.708333,607.348958 134.28125,595.5 C146.677083,581.645833 152.875,556.945312 152.875,521.398438 C152.875,481.841146 147.497396,456.59375 136.742188,445.65625 C126.169271,434.901042 112.588542,429.523438 96,429.523438 C73.03125,429.523438 57.0807292,440.005208 48.1484375,460.96875 C42.1328125,474.822917 39.125,494.966146 39.125,521.398438 C39.125,556.580729 44.3203125,580.643229 54.7109375,593.585938 C65.2838542,606.710938 79.046875,613.273438 96,613.273438 Z M96,594.679688 C80.3229167,594.679688 69.5677083,585.929688 63.734375,568.429688 C60.2708333,557.492188 58.5390625,541.815104 58.5390625,521.398438 C58.5390625,500.434896 60.2708333,484.666667 63.734375,474.09375 C69.5677083,456.59375 80.3229167,447.84375 96,447.84375 C106.9375,447.84375 115.869792,452.036458 122.796875,460.421875 C129.723958,468.625 133.1875,488.950521 133.1875,521.398438 C133.1875,548.924479 128.721354,568.885417 119.789062,581.28125 C113.044271,590.213542 105.114583,594.679688 96,594.679688 Z M236,613.273438 C250.947917,613.273438 263.708333,607.348958 274.28125,595.5 C286.677083,581.645833 292.875,556.945312 292.875,521.398438 C292.875,481.841146 287.497396,456.59375 276.742188,445.65625 C266.169271,434.901042 252.588542,429.523438 236,429.523438 C213.03125,429.523438 197.080729,440.005208 188.148438,460.96875 C182.132812,474.822917 179.125,494.966146 179.125,521.398438 C179.125,556.580729 184.320312,580.643229 194.710938,593.585938 C205.283854,606.710938 219.046875,613.273438 236,613.273438 Z M236,594.679688 C220.322917,594.679688 209.567708,585.929688 203.734375,568.429688 C200.270833,557.492188 198.539062,541.815104 198.539062,521.398438 C198.539062,500.434896 200.270833,484.666667 203.734375,474.09375 C209.567708,456.59375 220.322917,447.84375 236,447.84375 C246.9375,447.84375 255.869792,452.036458 262.796875,460.421875 C269.723958,468.625 273.1875,488.950521 273.1875,521.398438 C273.1875,548.924479 268.721354,568.885417 259.789062,581.28125 C253.044271,590.213542 245.114583,594.679688 236,594.679688 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <path
        d="M448.046406,613.273438 C462.994323,613.273438 475.75474,607.348958 486.327656,595.5 C498.72349,581.645833 504.921406,556.945312 504.921406,521.398438 C504.921406,481.841146 499.543802,456.59375 488.788594,445.65625 C478.215677,434.901042 464.634948,429.523438 448.046406,429.523438 C425.077656,429.523438 409.127136,440.005208 400.194844,460.96875 C394.179219,474.822917 391.171406,494.966146 391.171406,521.398438 C391.171406,556.580729 396.366719,580.643229 406.757344,593.585938 C417.330261,606.710938 431.093281,613.273438 448.046406,613.273438 Z M448.046406,594.679688 C432.369323,594.679688 421.614115,585.929688 415.780781,568.429688 C412.31724,557.492188 410.585469,541.815104 410.585469,521.398438 C410.585469,500.434896 412.31724,484.666667 415.780781,474.09375 C421.614115,456.59375 432.369323,447.84375 448.046406,447.84375 C458.983906,447.84375 467.916198,452.036458 474.843281,460.421875 C481.770365,468.625 485.233906,488.950521 485.233906,521.398438 C485.233906,548.924479 480.767761,568.885417 471.835469,581.28125 C465.090677,590.213542 457.16099,594.679688 448.046406,594.679688 Z M588.046406,613.273438 C602.994323,613.273438 615.75474,607.348958 626.327656,595.5 C638.72349,581.645833 644.921406,556.945312 644.921406,521.398438 C644.921406,481.841146 639.543802,456.59375 628.788594,445.65625 C618.215677,434.901042 604.634948,429.523438 588.046406,429.523438 C565.077656,429.523438 549.127136,440.005208 540.194844,460.96875 C534.179219,474.822917 531.171406,494.966146 531.171406,521.398438 C531.171406,556.580729 536.366719,580.643229 546.757344,593.585938 C557.330261,606.710938 571.093281,613.273438 588.046406,613.273438 Z M588.046406,594.679688 C572.369323,594.679688 561.614115,585.929688 555.780781,568.429688 C552.31724,557.492188 550.585469,541.815104 550.585469,521.398438 C550.585469,500.434896 552.31724,484.666667 555.780781,474.09375 C561.614115,456.59375 572.369323,447.84375 588.046406,447.84375 C598.983906,447.84375 607.916198,452.036458 614.843281,460.421875 C621.770365,468.625 625.233906,488.950521 625.233906,521.398438 C625.233906,548.924479 620.767761,568.885417 611.835469,581.28125 C605.090677,590.213542 597.16099,594.679688 588.046406,594.679688 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <circle fill="#999999" cx="341.846406" cy="487.3" r="12.8"></circle>
      <circle fill="#999999" cx="341.846406" cy="564.1" r="12.8"></circle>
      <path
        d="M1234.64641,161.623203 C1240.16925,161.623203 1244.64641,166.100356 1244.64641,171.623203 C1244.64641,177.052443 1240.31974,181.471135 1234.92631,181.619361 L1234.64641,181.623203 L947.446406,181.623203 C942.017166,181.623203 937.598475,185.949874 937.450248,191.343298 L937.446406,191.623203 L937.446406,478.823203 C937.446406,484.346051 932.969254,488.823203 927.446406,488.823203 C922.017166,488.823203 917.598475,484.496532 917.450248,479.103108 L917.446406,478.823203 L917.446406,191.623203 C917.446406,175.220346 930.610578,161.892146 946.950302,161.627222 L947.446406,161.623203 L1234.64641,161.623203 Z"
        fill="var(--dn-brand-color)"
        fillRule="nonzero"
        transform="translate(1081.046406, 325.223203) rotate(45.000000) translate(-1081.046406, -325.223203) "
      ></path>
      <path
        d="M1234.64641,535.623203 C1240.16925,535.623203 1244.64641,540.100356 1244.64641,545.623203 C1244.64641,551.052443 1240.31974,555.471135 1234.92631,555.619361 L1234.64641,555.623203 L947.446406,555.623203 C942.017166,555.623203 937.598475,559.949874 937.450248,565.343298 L937.446406,565.623203 L937.446406,852.823203 C937.446406,858.346051 932.969254,862.823203 927.446406,862.823203 C922.017166,862.823203 917.598475,858.496532 917.450248,853.103108 L917.446406,852.823203 L917.446406,565.623203 C917.446406,549.220346 930.610578,535.892146 946.950302,535.627222 L947.446406,535.623203 L1234.64641,535.623203 Z"
        fill="var(--dn-brand-color)"
        fillRule="nonzero"
        transform="translate(1081.046406, 699.223203) scale(1, -1) rotate(45.000000) translate(-1081.046406, -699.223203) "
      ></path>
      <path
        d="M835,613.273438 C849.947917,613.273438 862.708333,607.348958 873.28125,595.5 C885.677083,581.645833 891.875,556.945312 891.875,521.398438 C891.875,481.841146 886.497396,456.59375 875.742188,445.65625 C865.169271,434.901042 851.588542,429.523438 835,429.523438 C812.03125,429.523438 796.080729,440.005208 787.148438,460.96875 C781.132812,474.822917 778.125,494.966146 778.125,521.398438 C778.125,556.580729 783.320312,580.643229 793.710938,593.585938 C804.283854,606.710938 818.046875,613.273438 835,613.273438 Z M835,594.679688 C819.322917,594.679688 808.567708,585.929688 802.734375,568.429688 C799.270833,557.492188 797.539062,541.815104 797.539062,521.398438 C797.539062,500.434896 799.270833,484.666667 802.734375,474.09375 C808.567708,456.59375 819.322917,447.84375 835,447.84375 C845.9375,447.84375 854.869792,452.036458 861.796875,460.421875 C868.723958,468.625 872.1875,488.950521 872.1875,521.398438 C872.1875,548.924479 867.721354,568.885417 858.789062,581.28125 C852.044271,590.213542 844.114583,594.679688 835,594.679688 Z M975,613.273438 C989.947917,613.273438 1002.70833,607.348958 1013.28125,595.5 C1025.67708,581.645833 1031.875,556.945312 1031.875,521.398438 C1031.875,481.841146 1026.4974,456.59375 1015.74219,445.65625 C1005.16927,434.901042 991.588542,429.523438 975,429.523438 C952.03125,429.523438 936.080729,440.005208 927.148438,460.96875 C921.132812,474.822917 918.125,494.966146 918.125,521.398438 C918.125,556.580729 923.320312,580.643229 933.710938,593.585938 C944.283854,606.710938 958.046875,613.273438 975,613.273438 Z M975,594.679688 C959.322917,594.679688 948.567708,585.929688 942.734375,568.429688 C939.270833,557.492188 937.539062,541.815104 937.539062,521.398438 C937.539062,500.434896 939.270833,484.666667 942.734375,474.09375 C948.567708,456.59375 959.322917,447.84375 975,447.84375 C985.9375,447.84375 994.869792,452.036458 1001.79688,460.421875 C1008.72396,468.625 1012.1875,488.950521 1012.1875,521.398438 C1012.1875,548.924479 1007.72135,568.885417 998.789062,581.28125 C992.044271,590.213542 984.114583,594.679688 975,594.679688 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <path
        d="M1187.04641,613.273438 C1201.99432,613.273438 1214.75474,607.348958 1225.32766,595.5 C1237.72349,581.645833 1243.92141,556.945312 1243.92141,521.398438 C1243.92141,481.841146 1238.5438,456.59375 1227.78859,445.65625 C1217.21568,434.901042 1203.63495,429.523438 1187.04641,429.523438 C1164.07766,429.523438 1148.12714,440.005208 1139.19484,460.96875 C1133.17922,474.822917 1130.17141,494.966146 1130.17141,521.398438 C1130.17141,556.580729 1135.36672,580.643229 1145.75734,593.585938 C1156.33026,606.710938 1170.09328,613.273438 1187.04641,613.273438 Z M1187.04641,594.679688 C1171.36932,594.679688 1160.61411,585.929688 1154.78078,568.429688 C1151.31724,557.492188 1149.58547,541.815104 1149.58547,521.398438 C1149.58547,500.434896 1151.31724,484.666667 1154.78078,474.09375 C1160.61411,456.59375 1171.36932,447.84375 1187.04641,447.84375 C1197.98391,447.84375 1206.9162,452.036458 1213.84328,460.421875 C1220.77036,468.625 1224.23391,488.950521 1224.23391,521.398438 C1224.23391,548.924479 1219.76776,568.885417 1210.83547,581.28125 C1204.09068,590.213542 1196.16099,594.679688 1187.04641,594.679688 Z M1327.04641,613.273438 C1341.99432,613.273438 1354.75474,607.348958 1365.32766,595.5 C1377.72349,581.645833 1383.92141,556.945312 1383.92141,521.398438 C1383.92141,481.841146 1378.5438,456.59375 1367.78859,445.65625 C1357.21568,434.901042 1343.63495,429.523438 1327.04641,429.523438 C1304.07766,429.523438 1288.12714,440.005208 1279.19484,460.96875 C1273.17922,474.822917 1270.17141,494.966146 1270.17141,521.398438 C1270.17141,556.580729 1275.36672,580.643229 1285.75734,593.585938 C1296.33026,606.710938 1310.09328,613.273438 1327.04641,613.273438 Z M1327.04641,594.679688 C1311.36932,594.679688 1300.61411,585.929688 1294.78078,568.429688 C1291.31724,557.492188 1289.58547,541.815104 1289.58547,521.398438 C1289.58547,500.434896 1291.31724,484.666667 1294.78078,474.09375 C1300.61411,456.59375 1311.36932,447.84375 1327.04641,447.84375 C1337.98391,447.84375 1346.9162,452.036458 1353.84328,460.421875 C1360.77036,468.625 1364.23391,488.950521 1364.23391,521.398438 C1364.23391,548.924479 1359.76776,568.885417 1350.83547,581.28125 C1344.09068,590.213542 1336.16099,594.679688 1327.04641,594.679688 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <circle fill="#999999" cx="1080.84641" cy="487.3" r="12.8"></circle>
      <circle fill="#999999" cx="1080.84641" cy="564.1" r="12.8"></circle>
    </g>
  </svg>
)

export const NumberPickerSource = (
  <svg viewBox="0 0 1424 1024">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <path
        d="M1344,218 C1388.18278,218 1424,253.81722 1424,298 L1424,726 C1424,770.18278 1388.18278,806 1344,806 L80,806 C35.81722,806 0,770.18278 0,726 L0,298 C0,253.81722 35.81722,218 80,218 L1344,218 Z M1344,238 L80,238 C47.1942859,238 20.5378857,264.328343 20,297.00779 L20,298 L20,726 C20,758.805714 46.328343,785.462114 79.0077903,785.991962 L80,786 L1344,786 C1376.80571,786 1403.46211,759.671657 1404,726.99221 L1404,726 L1404,298 C1404,265.194286 1377.67166,238.537886 1344.99221,238.008038 L1344,238 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="#999999"
        transform="translate(1096.000000, 512.000000) scale(-1, 1) translate(-1096.000000, -512.000000) "
        x="1086"
        y="218"
        width="20"
        height="588"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(1260.000000, 512.000000) scale(-1, 1) rotate(90.000000) translate(-1260.000000, -512.000000) "
        x="1250"
        y="348"
        width="20"
        height="328"
        rx="10"
      ></rect>
      <path
        d="M1319.65311,602.492777 C1322.69467,597.882919 1328.89737,596.611559 1333.50722,599.653115 C1338.03621,602.64131 1339.3428,608.6807 1336.50299,613.263154 L1336.34689,613.507223 L1279.01464,700.401401 C1276.82002,703.727631 1273.9847,706.583294 1270.67424,708.801642 C1257.04789,717.932702 1238.64758,714.416301 1229.33159,700.989873 L1229.05208,700.579981 L1170.74424,613.56675 C1167.66981,608.978753 1168.8968,602.767127 1173.4848,599.692696 C1177.99087,596.673166 1184.06309,597.802804 1187.19117,602.190685 L1187.35885,602.43325 L1245.66669,689.446481 C1248.74112,694.034478 1254.95274,695.261466 1259.54074,692.187035 C1260.53388,691.52153 1261.39873,690.683951 1262.09506,689.714816 L1262.32087,689.386954 L1319.65311,602.492777 Z"
        fill="var(--dn-brand-color)"
        fillRule="nonzero"
      ></path>
      <path
        d="M1319.65311,314.492777 C1322.69467,309.882919 1328.89737,308.611559 1333.50722,311.653115 C1338.03621,314.64131 1339.3428,320.6807 1336.50299,325.263154 L1336.34689,325.507223 L1279.01464,412.401401 C1276.82002,415.727631 1273.9847,418.583294 1270.67424,420.801642 C1257.04789,429.932702 1238.64758,426.416301 1229.33159,412.989873 L1229.05208,412.579981 L1170.74424,325.56675 C1167.66981,320.978753 1168.8968,314.767127 1173.4848,311.692696 C1177.99087,308.673166 1184.06309,309.802804 1187.19117,314.190685 L1187.35885,314.43325 L1245.66669,401.446481 C1248.74112,406.034478 1254.95274,407.261466 1259.54074,404.187035 C1260.53388,403.52153 1261.39873,402.683951 1262.09506,401.714816 L1262.32087,401.386954 L1319.65311,314.492777 Z"
        fill="var(--dn-brand-color)"
        fillRule="nonzero"
        transform="translate(1253.525806, 367.940873) scale(1, -1) translate(-1253.525806, -367.940873) "
      ></path>
      <path
        d="M178.761719,654.887695 C188.066406,654.887695 192.71875,649.718424 192.71875,639.379883 L192.71875,639.379883 L192.71875,408.313477 C192.71875,398.750326 188.195638,393.96875 179.149414,393.96875 C176.564779,393.96875 172.817057,397.070312 167.90625,403.273438 C158.601562,415.16276 148.90918,422.787435 138.829102,426.147461 C135.469076,426.922852 132.755208,427.439779 130.6875,427.698242 C122.416667,429.765951 118.28125,433.513672 118.28125,438.941406 C118.28125,447.470703 122.804362,451.735352 131.850586,451.735352 C140.379883,451.735352 151.88151,447.21224 166.355469,438.166016 L166.355469,438.166016 L166.355469,639.379883 C166.355469,649.718424 170.490885,654.887695 178.761719,654.887695 Z M421.84668,649.072266 C432.443685,649.072266 437.742188,644.549154 437.742188,635.50293 C437.742188,626.973633 432.443685,622.708984 421.84668,622.708984 L421.84668,622.708984 L307.088867,622.708984 C317.944336,607.459635 331.772135,591.564128 348.572266,575.022461 C358.652344,565.200846 374.41862,550.985352 395.871094,532.375977 C423.009766,509.114258 436.579102,485.723307 436.579102,462.203125 C436.579102,440.233724 428.179036,423.045898 411.378906,410.639648 C396.646484,399.267253 378.166341,393.581055 355.938477,393.581055 C332.159831,393.581055 312.258138,401.334961 296.233398,416.842773 C280.725586,432.609049 272.97168,449.926107 272.97168,468.793945 C272.97168,479.132487 277.753255,484.301758 287.316406,484.301758 C292.485677,484.301758 295.974935,478.61556 297.78418,467.243164 C299.593424,456.129232 304.116536,446.824544 311.353516,439.329102 C323.501302,426.147461 338.362956,419.556641 355.938477,419.556641 C370.412435,419.556641 382.43099,423.045898 391.994141,430.024414 C402.591146,437.77832 407.889648,448.504557 407.889648,462.203125 C407.889648,480.295573 397.421875,498.646484 376.486328,517.255859 C358.39388,533.797526 344.953776,546.203776 336.166016,554.474609 C317.039714,571.016276 301.661133,586.394857 290.030273,600.610352 C276.848633,616.893555 270.257812,628.265951 270.257812,634.727539 C270.257812,644.29069 276.590169,649.072266 289.254883,649.072266 L289.254883,649.072266 L421.84668,649.072266 Z M547.847656,654.887695 C571.109375,654.887695 591.527995,648.813802 609.103516,636.666016 C629.263672,622.708984 639.34375,604.358073 639.34375,581.613281 C639.34375,548.271484 622.026693,526.04362 587.392578,514.929688 C619.442057,503.557292 635.466797,485.20638 635.466797,459.876953 C635.466797,437.649089 625.903646,420.590495 606.777344,408.701172 C591.269531,398.879557 571.884766,393.96875 548.623047,393.96875 C531.047526,393.96875 514.764323,399.008789 499.773438,409.088867 C482.714844,420.461263 474.185547,435.064453 474.185547,452.898438 C474.185547,462.978516 479.096354,468.018555 488.917969,468.018555 C494.08724,468.018555 497.705729,463.883138 499.773438,455.612305 C502.358073,445.273763 507.527344,437.132161 515.28125,431.1875 C525.102865,423.950521 536.216797,420.332031 548.623047,420.332031 C563.613932,420.332031 576.537109,423.17513 587.392578,428.861328 C600.832682,435.839844 607.552734,445.79069 607.552734,458.713867 C607.552734,475.513997 598.764974,487.40332 581.189453,494.381836 C570.333984,498.775716 554.050781,501.360352 532.339844,502.135742 C519.933594,502.652669 513.730469,507.046549 513.730469,515.317383 C513.730469,524.105143 519.933594,528.499023 532.339844,528.499023 C561.28776,528.499023 581.189453,532.763672 592.044922,541.292969 C604.451172,551.373047 610.654297,564.813151 610.654297,581.613281 C610.654297,596.08724 603.675781,607.718099 589.71875,616.505859 C577.829427,624.259766 563.872396,628.136719 547.847656,628.136719 C516.315104,628.136719 497.447266,614.825846 491.244141,588.204102 C488.659505,578.124023 485.041016,573.083984 480.388672,573.083984 C470.567057,573.083984 465.65625,578.770182 465.65625,590.142578 C465.65625,610.819661 474.702474,627.102865 492.794922,638.992188 C508.302734,649.589193 526.653646,654.887695 547.847656,654.887695 Z"
        stroke="#FFFFFF"
        fill="#999999"
        fillRule="nonzero"
      ></path>
    </g>
  </svg>
)

export const PasswordSource = {
  light: (
    <svg viewBox="0 0 1424 1024">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <path
          d="M1344,218 C1388.18278,218 1424,253.81722 1424,298 L1424,726 C1424,770.18278 1388.18278,806 1344,806 L80,806 C35.81722,806 0,770.18278 0,726 L0,298 C0,253.81722 35.81722,218 80,218 L1344,218 Z M1344,238 L80,238 C47.1942859,238 20.5378857,264.328343 20,297.00779 L20,298 L20,726 C20,758.805714 46.328343,785.462114 79.0077903,785.991962 L80,786 L1344,786 C1376.80571,786 1403.46211,759.671657 1404,726.99221 L1404,726 L1404,298 C1404,265.194286 1377.67166,238.537886 1344.99221,238.008038 L1344,238 Z"
          fill="#999999"
          fillRule="nonzero"
        ></path>
        <circle fill="#D8D8D8" cx="170" cy="512" r="50"></circle>
        <circle fill="#D8D8D8" cx="388" cy="512" r="50"></circle>
        <circle fill="#D8D8D8" cx="606" cy="512" r="50"></circle>
        <circle fill="#D8D8D8" cx="824" cy="512" r="50"></circle>
        <path
          d="M1154,452 C1120.86292,452 1094,478.862915 1094,512 C1094,545.137085 1120.86292,572 1154,572 C1187.13708,572 1214,545.137085 1214,512 C1214,478.862915 1187.13708,452 1154,452 Z M1154,472 C1176.09139,472 1194,489.90861 1194,512 C1194,534.09139 1176.09139,552 1154,552 C1131.90861,552 1114,534.09139 1114,512 C1114,489.90861 1131.90861,472 1154,472 Z"
          fill="var(--dn-brand-color)"
          fillRule="nonzero"
        ></path>
        <path
          d="M1154,395.333333 C1091.33002,395.333333 1035.15313,432.784592 985.698343,506.424832 L981.954212,512 L985.698343,517.575168 C1035.15313,591.215408 1091.33002,628.666667 1154,628.666667 C1216.66998,628.666667 1272.84687,591.215408 1322.30166,517.575168 L1326.04579,512 L1322.30166,506.424832 C1272.84687,432.784592 1216.66998,395.333333 1154,395.333333 Z M1155.65312,415.343433 C1208.50839,415.989823 1257.0702,447.668766 1301.54117,511.497045 L1301.889,511.999 L1301.54117,512.502955 C1256.60696,576.996112 1207.49624,608.666667 1154,608.666667 L1152.34688,608.656567 C1099.49161,608.010177 1050.9298,576.331234 1006.45883,512.502955 L1006.11,511.999 L1006.45883,511.497045 C1051.39304,447.003888 1100.50376,415.333333 1154,415.333333 L1155.65312,415.343433 Z"
          fill="#979797"
          fillRule="nonzero"
        ></path>
      </g>
    </svg>
  ),
  dark: (
    <svg viewBox="0 0 1424 1024">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <path
          d="M1344,218 C1388.18278,218 1424,253.81722 1424,298 L1424,726 C1424,770.18278 1388.18278,806 1344,806 L80,806 C35.81722,806 0,770.18278 0,726 L0,298 C0,253.81722 35.81722,218 80,218 L1344,218 Z M1344,238 L80,238 C47.1942859,238 20.5378857,264.328343 20,297.00779 L20,298 L20,726 C20,758.805714 46.328343,785.462114 79.0077903,785.991962 L80,786 L1344,786 C1376.80571,786 1403.46211,759.671657 1404,726.99221 L1404,726 L1404,298 C1404,265.194286 1377.67166,238.537886 1344.99221,238.008038 L1344,238 Z"
          fill="#999999"
          fillRule="nonzero"
        ></path>
        <circle fill="#4D4D4D" cx="170" cy="512" r="50"></circle>
        <circle fill="#4D4D4D" cx="388" cy="512" r="50"></circle>
        <circle fill="#4D4D4D" cx="606" cy="512" r="50"></circle>
        <circle fill="#4D4D4D" cx="824" cy="512" r="50"></circle>
        <path
          d="M1154,452 C1120.86292,452 1094,478.862915 1094,512 C1094,545.137085 1120.86292,572 1154,572 C1187.13708,572 1214,545.137085 1214,512 C1214,478.862915 1187.13708,452 1154,452 Z M1154,472 C1176.09139,472 1194,489.90861 1194,512 C1194,534.09139 1176.09139,552 1154,552 C1131.90861,552 1114,534.09139 1114,512 C1114,489.90861 1131.90861,472 1154,472 Z"
          fill="var(--dn-brand-color)"
          fillRule="nonzero"
        ></path>
        <path
          d="M1154,395.333333 C1091.33002,395.333333 1035.15313,432.784592 985.698343,506.424832 L981.954212,512 L985.698343,517.575168 C1035.15313,591.215408 1091.33002,628.666667 1154,628.666667 C1216.66998,628.666667 1272.84687,591.215408 1322.30166,517.575168 L1326.04579,512 L1322.30166,506.424832 C1272.84687,432.784592 1216.66998,395.333333 1154,395.333333 Z M1155.65312,415.343433 C1208.50839,415.989823 1257.0702,447.668766 1301.54117,511.497045 L1301.889,511.999 L1301.54117,512.502955 C1256.60696,576.996112 1207.49624,608.666667 1154,608.666667 L1152.34688,608.656567 C1099.49161,608.010177 1050.9298,576.331234 1006.45883,512.502955 L1006.11,511.999 L1006.45883,511.497045 C1051.39304,447.003888 1100.50376,415.333333 1154,415.333333 L1155.65312,415.343433 Z"
          fill="#979797"
          fillRule="nonzero"
        ></path>
      </g>
    </svg>
  ),
}

export const TransferSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M322,0 C370.127481,0 409.21135,38.8191143 409.988214,86.9753912 L410,88.4375 L410,218.839844 C410,224.362691 405.522847,228.839844 400,228.839844 C394.57076,228.839844 390.152068,224.513173 390.003842,219.119749 L390,218.839844 L390,88.4375 C390,51.006125 360.148902,20.6132787 323.124135,20.009165 L322,20 L88,20 C50.8326247,20 20.6098525,50.0185325 20.0091138,87.3053938 L20,88.4375 L20,935.5625 C20,972.993875 49.8510984,1003.38672 86.875865,1003.99084 L88,1004 L322,1004 C359.167375,1004 389.390148,973.981468 389.990886,936.694606 L390,935.5625 L390,811.042969 C390,805.520121 394.477153,801.042969 400,801.042969 C405.42924,801.042969 409.847932,805.36964 410,810.763064 L410,811.042969 L410,935.5625 C410,983.904398 371.397407,1023.20693 323.45561,1023.98815 L322,1024 L88,1024 C39.8725186,1024 0.788650371,985.180886 0.0117857556,937.024609 L0,935.5625 L0,88.4375 C0,40.0956016 38.6025934,0.793068282 86.5443898,0.0118518055 L88,0 L322,0 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M936,0 C984.127481,0 1023.21135,38.8191143 1023.98821,86.9753912 L1024,88.4375 L1024,218.839844 C1024,224.362691 1019.52285,228.839844 1014,228.839844 C1008.57076,228.839844 1004.15207,224.513173 1004.00384,219.119749 L1004,218.839844 L1004,88.4375 C1004,51.006125 974.148902,20.6132787 937.124135,20.009165 L936,20 L702,20 C664.832625,20 634.609852,50.0185325 634.009114,87.3053938 L634,88.4375 L634,935.5625 C634,972.993875 663.851098,1003.38672 700.875865,1003.99084 L702,1004 L936,1004 C973.167375,1004 1003.39015,973.981468 1003.99089,936.694606 L1004,935.5625 L1004,811.042969 C1004,805.520121 1008.47715,801.042969 1014,801.042969 C1019.42924,801.042969 1023.84793,805.36964 1024,810.763064 L1024,811.042969 L1024,935.5625 C1024,983.904398 985.397407,1023.20693 937.45561,1023.98815 L936,1024 L702,1024 C653.872519,1024 614.78865,985.180886 614.011786,937.024609 L614,935.5625 L614,88.4375 C614,40.0956016 652.602593,0.793068282 700.54439,0.0118518055 L702,0 L936,0 Z"
      fill="#999999"
      fillRule="nonzero"
      transform="translate(819.000000, 512.000000) scale(-1, 1) translate(-819.000000, -512.000000) "
    ></path>
    <rect
      fill="#999999"
      transform="translate(506.000000, 375.000000) scale(-1, 1) rotate(630.000000) translate(-506.000000, -375.000000) "
      x="496"
      y="95"
      width="20"
      height="560"
      rx="10"
    ></rect>
    <path
      d="M753,305 C769.402857,305 782.731057,318.164172 782.995981,334.503895 L783,335 L783,435 C783,440.522847 778.522847,445 773,445 C767.57076,445 763.152068,440.673329 763.003842,435.279905 L763,435 L763,335 C763,329.57076 758.673329,325.152068 753.279905,325.003842 L753,325 L653,325 C647.477153,325 643,320.522847 643,315 C643,309.57076 647.326671,305.152068 652.720095,305.003842 L653,305 L753,305 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(713.000000, 375.000000) rotate(45.000000) translate(-713.000000, -375.000000) "
    ></path>
    <rect
      fill="#999999"
      transform="translate(518.000000, 649.000000) rotate(630.000000) translate(-518.000000, -649.000000) "
      x="508"
      y="369"
      width="20"
      height="560"
      rx="10"
    ></rect>
    <path
      d="M351,579 C367.402857,579 380.731057,592.164172 380.995981,608.503895 L381,609 L381,709 C381,714.522847 376.522847,719 371,719 C365.57076,719 361.152068,714.673329 361.003842,709.279905 L361,709 L361,609 C361,603.57076 356.673329,599.152068 351.279905,599.003842 L351,599 L251,599 C245.477153,599 241,594.522847 241,589 C241,583.57076 245.326671,579.152068 250.720095,579.003842 L251,579 L351,579 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(311.000000, 649.000000) scale(-1, 1) rotate(45.000000) translate(-311.000000, -649.000000) "
    ></path>
  </g>
)

export const UploadSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <rect
      fill="#999999"
      transform="translate(502.000000, 712.000000) scale(-1, 1) rotate(900.000000) translate(-502.000000, -712.000000) "
      x="492"
      y="512"
      width="20"
      height="400"
      rx="10"
    ></rect>
    <path
      d="M622,551.705627 C627.522847,551.705627 632,556.18278 632,561.705627 C632,567.134867 627.673329,571.553559 622.279905,571.701786 L622,571.705627 L402,571.705627 C396.57076,571.705627 392.152068,576.032298 392.003842,581.425723 L392,581.705627 L392,801.705627 C392,807.228475 387.522847,811.705627 382,811.705627 C376.57076,811.705627 372.152068,807.378957 372.003842,801.985532 L372,801.705627 L372,581.705627 C372,565.30277 385.164172,551.97457 401.503895,551.709647 L402,551.705627 L622,551.705627 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(502.000000, 681.705627) rotate(45.000000) translate(-502.000000, -681.705627) "
    ></path>
    <path
      d="M524.931034,80 C691.960872,80 786.973493,198.134474 809.205262,430.123156 C809.683377,435.112294 813.780979,438.97686 818.789541,439.162362 C952.942877,444.13101 1022,506.676517 1022,624.482759 C1022,686.378487 997.330268,733.50153 948.40537,764.65293 C900.561062,795.116298 829.824906,810.12931 736.137931,810.12931 C730.615084,810.12931 726.137931,805.652158 726.137931,800.12931 C726.137931,794.606463 730.615084,790.12931 736.137931,790.12931 C826.359002,790.12931 893.553881,775.867897 937.663596,747.782415 C980.692721,720.384964 1002,679.684731 1002,624.482759 C1002,519.142921 943.197337,464.83155 821.748746,459.301313 L818.049306,459.148659 C803.023623,458.592151 790.730816,446.998455 789.296471,432.03104 C767.947106,209.250259 680.079839,100 524.931034,100 C369.7047,100 282.288484,189.964836 260.728055,373.294922 C259.087578,387.244033 247.995507,398.167549 234.064277,399.627152 L233.641257,399.668479 C91.431391,412.557268 22,481.319011 22,608.318966 C22,739.159701 103.629541,799.201265 272.239445,790.143708 C277.754341,789.847453 282.46521,794.077998 282.761465,799.592894 C283.057719,805.10779 278.827175,809.818658 273.312279,810.114913 C94.2210335,819.735517 2,751.903517 2,608.318966 C2,470.51406 79.7276986,393.536025 231.836012,379.750118 C236.545747,379.323264 240.312596,375.655603 240.864945,370.958932 C263.543681,178.119782 358.883821,80 524.931034,80 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
  </g>
)

export const UploadDraggerSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M524.931034,80 C691.960872,80 786.973493,198.134474 809.205262,430.123156 C809.683377,435.112294 813.780979,438.97686 818.789541,439.162362 C952.942877,444.13101 1022,506.676517 1022,624.482759 C1022,686.378487 997.330268,733.50153 948.40537,764.65293 C900.561062,795.116298 829.824906,810.12931 736.137931,810.12931 C730.615084,810.12931 726.137931,805.652158 726.137931,800.12931 C726.137931,794.606463 730.615084,790.12931 736.137931,790.12931 C826.359002,790.12931 893.553881,775.867897 937.663596,747.782415 C980.692721,720.384964 1002,679.684731 1002,624.482759 C1002,519.142921 943.197337,464.83155 821.748746,459.301313 L818.049306,459.148659 C803.023623,458.592151 790.730816,446.998455 789.296471,432.03104 C767.947106,209.250259 680.079839,100 524.931034,100 C369.7047,100 282.288484,189.964836 260.728055,373.294922 C259.087578,387.244033 247.995507,398.167549 234.064277,399.627152 L233.641257,399.668479 C91.431391,412.557268 22,481.319011 22,608.318966 C22,739.159701 103.629541,799.201265 272.239445,790.143708 C277.754341,789.847453 282.46521,794.077998 282.761465,799.592894 C283.057719,805.10779 278.827175,809.818658 273.312279,810.114913 C94.2210335,819.735517 2,751.903517 2,608.318966 C2,470.51406 79.7276986,393.536025 231.836012,379.750118 C236.545747,379.323264 240.312596,375.655603 240.864945,370.958932 C263.543681,178.119782 358.883821,80 524.931034,80 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M439.823288,475.014091 C464.593574,475.014091 485.397758,491.949828 485.811365,513.998824 L485.81764,514.668528 L485.81764,702.479967 C485.81764,708.002814 481.340488,712.479967 475.81764,712.479967 C470.3884,712.479967 465.969709,708.153296 465.821482,702.759871 L465.81764,702.479967 L465.81764,514.668528 C465.81764,504.287977 454.561061,495.014091 439.823288,495.014091 C425.232893,495.014091 414.054416,504.103426 413.832306,514.357452 L413.828937,514.668528 L413.828937,702.479967 C413.828937,708.002814 409.351784,712.479967 403.828937,712.479967 C398.399697,712.479967 393.981005,708.153296 393.832778,702.759871 L393.828937,702.479967 L393.828937,514.668528 C393.828937,492.293692 414.802798,475.014091 439.823288,475.014091 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M318.16468,621.831009 C339.943451,609.257029 367.731162,616.518628 380.608496,638.010491 L381.064533,638.788048 L412.578575,697.104545 C415.204243,701.963323 413.393949,708.030664 408.535172,710.656331 C403.761636,713.235933 397.821571,711.533858 395.124732,706.86583 L394.983386,706.612927 L363.622,648.58 L363.455677,648.295578 C356.251384,636.271842 340.789662,632.128886 328.538694,638.939601 L328.16468,639.151517 C317.606783,645.247121 312.168986,671.880929 316.109417,695.352405 L316.261878,696.233685 L316.286102,696.405645 C335.959075,836.064043 400.678916,904.190447 512.004302,904.239372 C622.863427,904.287067 678.799957,838.186966 681.78614,701.57069 C681.90683,696.049161 686.480753,691.670917 692.002281,691.791607 C697.52381,691.912298 701.902054,696.48622 701.7838,702.007749 C698.573653,848.758808 634.654348,924.292141 511.995605,924.239398 C391.025232,924.186206 318.897861,849.53522 297.125965,703.638324 L296.502,699.339 L296.37707,698.61158 C294.258457,685.895152 294.347781,671.15136 296.807723,658.224662 L296.991952,657.282105 C300.285113,640.870794 307.155309,628.187272 318.16468,621.831009 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M583.800696,475.014091 C608.570982,475.014091 629.375166,491.949828 629.788773,513.998824 L629.795048,514.668528 L629.795048,702.479967 L609.795048,702.479967 L609.795048,514.668528 C609.795048,504.287977 598.538469,495.014091 583.800696,495.014091 C569.210301,495.014091 558.031824,504.103426 557.809713,514.357452 L557.806344,514.668528 L557.806344,702.479967 L537.806344,702.479967 L537.806344,514.668528 C537.806344,492.293692 558.780206,475.014091 583.800696,475.014091 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M655.7894,539.503971 C680.557113,539.503971 701.363818,556.41543 701.777476,578.443167 L701.783752,579.112228 L701.783752,702.479967 C701.783752,708.002814 697.306599,712.479967 691.783752,712.479967 C686.354512,712.479967 681.93582,708.153296 681.787594,702.759871 L681.783752,702.479967 L681.783752,579.112228 C681.783752,568.761277 670.529771,559.503971 655.7894,559.503971 C641.196432,559.503971 630.020476,568.577057 629.798416,578.802036 L629.795048,579.112228 L629.795048,702.479967 C629.795048,708.002814 625.317896,712.479967 619.795048,712.479967 C614.365808,712.479967 609.947116,708.153296 609.79889,702.759871 L609.795048,702.479967 L609.795048,579.112228 C609.795048,556.758801 630.771508,539.503971 655.7894,539.503971 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M511.811992,416.523269 C536.577398,416.523269 557.386365,433.412918 557.800067,455.421574 L557.806344,456.090058 L557.806344,702.479967 C557.806344,708.002814 553.329192,712.479967 547.806344,712.479967 C542.377104,712.479967 537.958412,708.153296 537.810186,702.759871 L537.806344,702.479967 L537.806344,456.090058 C537.806344,445.765696 526.554694,436.523269 511.811992,436.523269 C497.216718,436.523269 486.043022,445.581771 485.821008,455.78066 L485.81764,456.090058 L485.81764,702.479967 C485.81764,708.002814 481.340488,712.479967 475.81764,712.479967 C470.3884,712.479967 465.969709,708.153296 465.821482,702.759871 L465.81764,702.479967 L465.81764,456.090058 C465.81764,433.755846 486.796431,416.523269 511.811992,416.523269 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M441.678633,797.281949 C445.757175,793.558063 452.082294,793.845568 455.806181,797.924111 C472.937361,816.686831 491.518588,825.779772 512,825.779772 C532.26195,825.779772 549.247619,816.9009 563.64541,798.503722 C567.049187,794.154452 573.334273,793.387978 577.683543,796.791754 C582.032813,800.195531 582.799287,806.480617 579.395511,810.829887 C561.327597,833.916666 538.632958,845.779772 512,845.779772 C485.586503,845.779772 461.781949,834.130735 441.036471,811.409498 C437.312584,807.330955 437.60009,801.005836 441.678633,797.281949 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
  </g>
)

export const SwitchSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M764,252 C907.594035,252 1024,368.405965 1024,512 C1024,655.594035 907.594035,772 764,772 L260,772 C116.405965,772 0,655.594035 0,512 C0,368.405965 116.405965,252 260,252 L764,252 Z M764,272 L260,272 C127.45166,272 20,379.45166 20,512 C20,643.222857 125.313372,749.848457 256.031161,751.967847 L260,752 L764,752 C896.54834,752 1004,644.54834 1004,512 C1004,380.777143 898.686628,274.151543 767.968839,272.032153 L764,272 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M764,342 C670.111593,342 594,418.111593 594,512 C594,605.888407 670.111593,682 764,682 C857.888407,682 934,605.888407 934,512 C934,418.111593 857.888407,342 764,342 Z M764,362 C846.842712,362 914,429.157288 914,512 C914,594.842712 846.842712,662 764,662 C681.157288,662 614,594.842712 614,512 C614,429.157288 681.157288,362 764,362 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
  </g>
)

export const ObjectSource = (
  <svg viewBox="0 0 1424 1024">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <rect
        fill="#FFFFFF"
        opacity="0"
        x="0"
        y="0"
        width="1423"
        height="1024"
      ></rect>
      <g
        transform="translate(28.269531, 358.253906)"
        fillRule="nonzero"
        stroke="#FFFFFF"
        strokeWidth="0.5"
      >
        <path
          d="M73.0078125,335.507812 C77.109375,335.507812 80.6640625,334.003906 83.671875,330.996094 C86.953125,328.261719 88.59375,324.84375 88.59375,320.742188 C88.59375,317.734375 85.859375,314.453125 80.390625,310.898438 C72.734375,306.25 67.8125,302.421875 65.625,299.414062 C60.4296875,293.671875 57.8320312,286.289062 57.8320312,277.265625 L57.8320312,277.265625 L57.8320312,208.359375 C57.8320312,192.5 51.2695312,179.238281 38.1445312,168.574219 C51.2695312,156.816406 57.8320312,143.007812 57.8320312,127.148438 L57.8320312,127.148438 L57.8320312,59.0625 C57.8320312,50.3125 60.4296875,42.7929688 65.625,36.5039062 C67.8125,34.0429688 72.734375,30.3515625 80.390625,25.4296875 C85.859375,21.6015625 88.59375,18.3203125 88.59375,15.5859375 C88.59375,5.1953125 83.3984375,0 73.0078125,0 C70.546875,0 66.8554688,1.77734375 61.9335938,5.33203125 C40.6054688,18.7304688 29.9414062,36.640625 29.9414062,59.0625 L29.9414062,59.0625 L29.9414062,127.148438 C29.9414062,133.710938 27.7539062,140.136719 23.3789062,146.425781 C22.5585938,147.792969 18.046875,151.210938 9.84375,156.679688 C3.28125,161.601562 0,165.566406 0,168.574219 C0,171.582031 3.28125,175.136719 9.84375,179.238281 C18.046875,184.160156 22.5585938,187.304688 23.3789062,188.671875 C27.7539062,194.960937 29.9414062,201.523438 29.9414062,208.359375 L29.9414062,208.359375 L29.9414062,277.265625 C29.9414062,298.320312 38.28125,314.453125 54.9609375,325.664062 C64.53125,332.226562 70.546875,335.507812 73.0078125,335.507812 Z"
          fill="var(--dn-brand-color)"
        ></path>
        <path
          d="M252.65625,305.15625 C292.578125,305.15625 323.339844,291.484375 344.941406,264.140625 C364.355469,240.078125 374.0625,208.222656 374.0625,168.574219 C374.0625,127.832031 364.355469,95.15625 344.941406,70.546875 C323.613281,42.9296875 292.851562,29.1210938 252.65625,29.1210938 C211.914062,29.1210938 181.015625,42.7929688 159.960938,70.1367188 C141.09375,94.7460938 131.660156,127.421875 131.660156,168.164062 C131.660156,207.8125 141.230469,239.804688 160.371094,264.140625 C181.425781,291.484375 212.1875,305.15625 252.65625,305.15625 Z M252.65625,275.214844 C223.398438,275.214844 200.839844,264.140625 184.980469,241.992188 C171.035156,222.578125 164.0625,197.832031 164.0625,167.753906 C164.0625,136.855469 171.035156,111.5625 184.980469,91.875 C200.566406,69.7265625 223.125,58.6523438 252.65625,58.6523438 C281.914062,58.6523438 304.472656,69.8632812 320.332031,92.2851562 C334.277344,112.246094 341.25,137.675781 341.25,168.574219 C341.25,198.378906 334.277344,222.988281 320.332031,242.402344 C304.199219,264.277344 281.640625,275.214844 252.65625,275.214844 Z M498.339844,305.15625 C523.769531,305.15625 545.234375,295.175781 562.734375,275.214844 C579.414062,256.074219 587.753906,233.652344 587.753906,207.949219 C587.753906,181.972656 579.277344,159.6875 562.324219,141.09375 C545.097656,123.046875 523.769531,114.023438 498.339844,114.023438 C472.910156,114.023438 452.675781,124.140625 437.636719,144.375 L437.636719,144.375 L437.636719,46.3476562 C437.636719,34.8632812 432.578125,29.1210938 422.460938,29.1210938 C412.617188,29.1210938 407.695312,34.8632812 407.695312,46.3476562 L407.695312,46.3476562 L407.695312,287.519531 C407.695312,299.277344 412.617188,305.15625 422.460938,305.15625 C432.578125,305.15625 437.636719,299.277344 437.636719,287.519531 L437.636719,287.519531 L437.636719,273.164062 C449.667969,294.492188 469.902344,305.15625 498.339844,305.15625 Z M498.339844,275.214844 C480.292969,275.214844 465.664062,268.652344 454.453125,255.527344 C443.242188,242.402344 437.636719,226.542969 437.636719,207.949219 C437.636719,188.808594 443.105469,173.359375 454.042969,161.601562 C464.980469,149.84375 479.746094,143.964844 498.339844,143.964844 C515.292969,143.964844 528.964844,150.390625 539.355469,163.242188 C550.292969,175.820312 555.761719,190.722656 555.761719,207.949219 C555.761719,226.269531 550.839844,241.855469 540.996094,254.707031 C530.058594,268.378906 515.839844,275.214844 498.339844,275.214844 Z M680.449219,70.546875 C686.191406,70.546875 691.113281,68.6328125 695.214844,64.8046875 C699.316406,60.703125 701.367188,55.6445312 701.367188,49.6289062 C701.367188,43.8867188 699.316406,38.828125 695.214844,34.453125 C691.113281,30.625 686.191406,28.7109375 680.449219,28.7109375 C674.433594,28.7109375 669.375,30.625 665.273438,34.453125 C661.171875,38.828125 659.121094,43.8867188 659.121094,49.6289062 C659.121094,55.6445312 661.171875,60.703125 665.273438,64.8046875 C669.375,68.6328125 674.433594,70.546875 680.449219,70.546875 Z M625.078125,355.605469 C648.867188,355.605469 665,353.691406 673.476562,349.863281 C687.421875,344.121094 694.394531,331.542969 694.394531,312.128906 L694.394531,312.128906 L694.394531,131.660156 C694.394531,119.628906 689.609375,113.613281 680.039062,113.613281 C670.195312,113.613281 665.273438,119.628906 665.273438,131.660156 L665.273438,131.660156 L665.273438,300.234375 C665.273438,311.445312 661.445312,318.691406 653.789062,321.972656 C649.414062,323.886719 640.664062,324.84375 627.539062,324.84375 C618.789062,324.84375 612.773438,325.664062 609.492188,327.304688 C604.84375,329.492188 602.519531,333.867188 602.519531,340.429688 C602.519531,350.546875 610.039062,355.605469 625.078125,355.605469 Z M825.644531,305.15625 C852.988281,305.15625 874.316406,296.132812 889.628906,278.085938 C900.019531,265.78125 905.214844,255.800781 905.214844,248.144531 C905.214844,243.496094 903.574219,239.53125 900.292969,236.25 C897.285156,232.695312 893.867188,230.917969 890.039062,230.917969 C884.023438,230.917969 879.648438,233.378906 876.914062,238.300781 C870.078125,250.332031 864.0625,258.671875 858.867188,263.320312 C850.117188,271.25 839.316406,275.214844 826.464844,275.214844 C810.332031,275.214844 797.207031,269.472656 787.089844,257.988281 C777.519531,247.324219 772.050781,233.378906 770.683594,216.152344 L770.683594,216.152344 L891.269531,216.152344 C903.574219,216.152344 909.726562,211.09375 909.726562,200.976562 C909.726562,178.28125 902.207031,158.320312 887.167969,141.09375 C871.308594,123.046875 850.800781,114.023438 825.644531,114.023438 C801.035156,114.023438 780.390625,123.320312 763.710938,141.914062 C747.304688,160.78125 739.101562,182.65625 739.101562,207.539062 C739.101562,235.15625 746.484375,257.988281 761.25,276.035156 C777.109375,295.449219 798.574219,305.15625 825.644531,305.15625 Z M878.554688,188.671875 C878.828125,188.945312 878.144531,188.945312 876.503906,188.671875 L876.503906,188.671875 L772.734375,188.671875 C776.015625,175.273438 782.441406,164.472656 792.011719,156.269531 C801.308594,147.792969 812.519531,143.554688 825.644531,143.554688 C842.324219,143.554688 854.902344,147.929688 863.378906,156.679688 C873.496094,167.34375 878.554688,178.007812 878.554688,188.671875 Z M1023.75,305.566406 C1053.82812,305.566406 1077.89062,291.621094 1095.9375,263.730469 C1102.5,253.339844 1105.78125,246.367188 1105.78125,242.8125 C1105.78125,231.601562 1100.17578,225.996094 1088.96484,225.996094 C1083.22266,225.996094 1078.30078,229.960938 1074.19922,237.890625 C1060.25391,263.046875 1043.84766,275.625 1024.98047,275.625 C1007.48047,275.625 993.125,268.925781 981.914062,255.527344 C971.796875,243.222656 966.738281,228.046875 966.738281,210 C966.738281,192.226562 971.386719,177.050781 980.683594,164.472656 C991.074219,150.527344 1005.01953,143.554688 1022.51953,143.554688 C1033.45703,143.554688 1043.98438,146.5625 1054.10156,152.578125 C1062.57812,157.773438 1069.41406,165.703125 1074.60938,176.367188 C1077.34375,182.109375 1081.17188,184.980469 1086.09375,184.980469 C1097.30469,184.980469 1102.91016,179.375 1102.91016,168.164062 C1102.91016,155.859375 1095.39062,143.964844 1080.35156,132.480469 C1064.49219,120.175781 1045.625,114.023438 1023.75,114.023438 C998.046875,114.023438 976.855469,123.457031 960.175781,142.324219 C943.496094,161.464844 935.15625,184.023438 935.15625,210 C935.15625,235.976562 943.359375,258.125 959.765625,276.445312 C976.992188,295.859375 998.320312,305.566406 1023.75,305.566406 Z M1226.77734,299.003906 C1238.26172,299.003906 1244.00391,294.082031 1244.00391,284.238281 C1244.00391,274.394531 1238.26172,269.472656 1226.77734,269.472656 L1226.77734,269.472656 L1199.29688,269.472656 C1195.19531,269.472656 1193.14453,264.003906 1193.14453,253.066406 L1193.14453,253.066406 L1193.14453,149.296875 L1224.31641,149.296875 C1235.80078,149.296875 1241.54297,144.238281 1241.54297,134.121094 C1241.54297,124.003906 1235.80078,118.945312 1224.31641,118.945312 L1224.31641,118.945312 L1193.14453,118.945312 L1193.14453,86.5429688 C1193.14453,74.7851562 1188.08594,68.90625 1177.96875,68.90625 C1168.67188,68.90625 1164.02344,74.7851562 1164.02344,86.5429688 L1164.02344,86.5429688 L1164.02344,118.945312 L1141.875,118.945312 C1130.39062,118.945312 1124.64844,124.003906 1124.64844,134.121094 C1124.64844,144.238281 1130.39062,149.296875 1141.875,149.296875 L1141.875,149.296875 L1164.02344,149.296875 L1164.02344,254.296875 C1164.02344,283.828125 1174.00391,298.59375 1193.96484,298.59375 C1197.79297,298.59375 1203.26172,298.730469 1210.37109,299.003906 L1210.37109,299.003906 L1226.77734,299.003906 Z"
          fill="#999999"
        ></path>
        <path
          d="M1294.45312,335.507812 C1296.91406,335.507812 1302.92969,332.226562 1312.5,325.664062 C1329.17969,314.453125 1337.51953,298.320312 1337.51953,277.265625 L1337.51953,277.265625 L1337.51953,208.359375 C1337.51953,201.523438 1339.70703,194.960937 1344.08203,188.671875 C1344.90234,187.304688 1349.41406,184.160156 1357.61719,179.238281 C1364.45312,175.136719 1367.87109,171.582031 1367.87109,168.574219 C1367.87109,165.566406 1364.45312,161.601562 1357.61719,156.679688 C1349.41406,151.210938 1344.90234,147.792969 1344.08203,146.425781 C1339.70703,140.136719 1337.51953,133.710938 1337.51953,127.148438 L1337.51953,127.148438 L1337.51953,59.0625 C1337.51953,36.640625 1326.85547,18.7304688 1305.52734,5.33203125 C1300.60547,1.77734375 1296.91406,0 1294.45312,0 C1284.33594,0 1279.27734,5.1953125 1279.27734,15.5859375 C1279.27734,18.3203125 1281.875,21.6015625 1287.07031,25.4296875 C1294.72656,30.3515625 1299.64844,34.0429688 1301.83594,36.5039062 C1307.03125,42.7929688 1309.62891,50.3125 1309.62891,59.0625 L1309.62891,59.0625 L1309.62891,127.148438 C1309.62891,143.007812 1316.19141,156.816406 1329.31641,168.574219 C1316.19141,179.238281 1309.62891,192.5 1309.62891,208.359375 L1309.62891,208.359375 L1309.62891,277.265625 C1309.62891,286.289062 1307.03125,293.671875 1301.83594,299.414062 C1299.64844,302.421875 1294.72656,306.25 1287.07031,310.898438 C1281.875,314.453125 1279.27734,317.734375 1279.27734,320.742188 C1279.27734,324.84375 1280.78125,328.261719 1283.78906,330.996094 C1286.79688,334.003906 1290.35156,335.507812 1294.45312,335.507812 Z"
          fill="var(--dn-brand-color)"
        ></path>
      </g>
    </g>
  </svg>
)

export const CardSource = {
  light: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <path
        d="M65,17 L962,17 C990.718807,17 1014,40.281193 1014,69 L1014,267 L1014,267 L13,267 L13,69 C13,40.281193 36.281193,17 65,17 Z"
        fill="#D8D8D8"
      ></path>
      <path
        d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="#999999"
        transform="translate(513.500000, 267.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -267.000000) "
        x="503.5"
        y="-233.5"
        width="20"
        height="1001"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(340.000000, 142.000000) scale(-1, 1) rotate(630.000000) translate(-340.000000, -142.000000) "
        x="330"
        y="-108"
        width="20"
        height="500"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(824.000000, 142.000000) scale(-1, 1) rotate(630.000000) translate(-824.000000, -142.000000) "
        x="814"
        y="42"
        width="20"
        height="200"
        rx="10"
      ></rect>
    </g>
  ),
  dark: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <path
        d="M65,17 L962,17 C990.718807,17 1014,40.281193 1014,69 L1014,267 L1014,267 L13,267 L13,69 C13,40.281193 36.281193,17 65,17 Z"
        fill="#4D4D4D"
      ></path>
      <path
        d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="#999999"
        transform="translate(513.500000, 267.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -267.000000) "
        x="503.5"
        y="-233.5"
        width="20"
        height="1001"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(340.000000, 142.000000) scale(-1, 1) rotate(630.000000) translate(-340.000000, -142.000000) "
        x="330"
        y="-108"
        width="20"
        height="500"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(824.000000, 142.000000) scale(-1, 1) rotate(630.000000) translate(-824.000000, -142.000000) "
        x="814"
        y="42"
        width="20"
        height="200"
        rx="10"
      ></rect>
    </g>
  ),
}

export const GridSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(512.000000, 252.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -252.000000) "
      x="502"
      y="-148"
      width="20"
      height="800"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(512.000000, 512.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -512.000000) "
      x="502"
      y="112"
      width="20"
      height="800"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(512.000000, 772.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -772.000000) "
      x="502"
      y="372"
      width="20"
      height="800"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(772.000000, 512.000000) scale(-1, 1) rotate(720.000000) translate(-772.000000, -512.000000) "
      x="762"
      y="112"
      width="20"
      height="800"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(512.000000, 512.000000) scale(-1, 1) rotate(720.000000) translate(-512.000000, -512.000000) "
      x="502"
      y="112"
      width="20"
      height="800"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(252.000000, 512.000000) scale(-1, 1) rotate(720.000000) translate(-252.000000, -512.000000) "
      x="242"
      y="112"
      width="20"
      height="800"
      rx="10"
    ></rect>
  </g>
)

export const FormLayoutSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(512.000000, 512.000000) scale(-1, 1) translate(-512.000000, -512.000000) "
      x="502"
      y="11.5"
      width="20"
      height="1001"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(251.000000, 292.000000) scale(-1, 1) rotate(630.000000) translate(-251.000000, -292.000000) "
      x="241"
      y="192"
      width="20"
      height="200"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(773.000000, 292.000000) scale(-1, 1) rotate(630.000000) translate(-773.000000, -292.000000) "
      x="763"
      y="192"
      width="20"
      height="200"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(251.000000, 512.000000) scale(-1, 1) rotate(630.000000) translate(-251.000000, -512.000000) "
      x="241"
      y="412"
      width="20"
      height="200"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(773.000000, 512.000000) scale(-1, 1) rotate(630.000000) translate(-773.000000, -512.000000) "
      x="763"
      y="412"
      width="20"
      height="200"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(251.000000, 732.000000) scale(-1, 1) rotate(630.000000) translate(-251.000000, -732.000000) "
      x="241"
      y="632"
      width="20"
      height="200"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(773.000000, 732.000000) scale(-1, 1) rotate(630.000000) translate(-773.000000, -732.000000) "
      x="763"
      y="632"
      width="20"
      height="200"
      rx="10"
    ></rect>
  </g>
)

export const SpaceSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M294.928581,105.844361 C300.146969,107.652751 302.911317,113.349081 301.102927,118.567468 C299.325715,123.695883 293.793476,126.454152 288.65014,124.831284 L288.379819,124.741813 C283.16358,122.934167 277.652303,122 272,122 C266.477153,122 262,117.522847 262,112 C262,106.477153 266.477153,102 272,102 C279.888963,102 287.613158,103.309259 294.928581,105.844361 Z M340.088269,155.693363 C341.370742,161.065244 338.055622,166.459664 332.683741,167.742137 C327.31186,169.02461 321.91744,165.70949 320.634967,160.337609 C319.338201,154.905862 317.138154,149.754737 314.137952,145.071652 C311.158708,140.421281 312.513424,134.236256 317.163795,131.257012 C321.814166,128.277768 327.999191,129.632484 330.978435,134.282855 C335.182,140.844298 338.268511,148.070963 340.088269,155.693363 Z M342,217.890597 C342,223.413445 337.522847,227.890597 332,227.890597 C326.477153,227.890597 322,223.413445 322,217.890597 L322,197.890597 C322,192.36775 326.477153,187.890597 332,187.890597 C337.522847,187.890597 342,192.36775 342,197.890597 L342,217.890597 Z M342,277.890597 C342,283.413445 337.522847,287.890597 332,287.890597 C326.477153,287.890597 322,283.413445 322,277.890597 L322,257.890597 C322,252.36775 326.477153,247.890597 332,247.890597 C337.522847,247.890597 342,252.36775 342,257.890597 L342,277.890597 Z M342,337.890597 C342,343.413445 337.522847,347.890597 332,347.890597 C326.477153,347.890597 322,343.413445 322,337.890597 L322,317.890597 C322,312.36775 326.477153,307.890597 332,307.890597 C337.522847,307.890597 342,312.36775 342,317.890597 L342,337.890597 Z M342,397.890597 C342,403.413445 337.522847,407.890597 332,407.890597 C326.477153,407.890597 322,403.413445 322,397.890597 L322,377.890597 C322,372.36775 326.477153,367.890597 332,367.890597 C337.522847,367.890597 342,372.36775 342,377.890597 L342,397.890597 Z M342,457.890597 C342,463.413445 337.522847,467.890597 332,467.890597 C326.477153,467.890597 322,463.413445 322,457.890597 L322,437.890597 C322,432.36775 326.477153,427.890597 332,427.890597 C337.522847,427.890597 342,432.36775 342,437.890597 L342,457.890597 Z M342,517.890597 C342,523.413445 337.522847,527.890597 332,527.890597 C326.477153,527.890597 322,523.413445 322,517.890597 L322,497.890597 C322,492.36775 326.477153,487.890597 332,487.890597 C337.522847,487.890597 342,492.36775 342,497.890597 L342,517.890597 Z M342,577.890597 C342,583.413445 337.522847,587.890597 332,587.890597 C326.477153,587.890597 322,583.413445 322,577.890597 L322,557.890597 C322,552.36775 326.477153,547.890597 332,547.890597 C337.522847,547.890597 342,552.36775 342,557.890597 L342,577.890597 Z M342,637.890597 C342,643.413445 337.522847,647.890597 332,647.890597 C326.477153,647.890597 322,643.413445 322,637.890597 L322,617.890597 C322,612.36775 326.477153,607.890597 332,607.890597 C337.522847,607.890597 342,612.36775 342,617.890597 L342,637.890597 Z M342,697.890597 C342,703.413445 337.522847,707.890597 332,707.890597 C326.477153,707.890597 322,703.413445 322,697.890597 L322,677.890597 C322,672.36775 326.477153,667.890597 332,667.890597 C337.522847,667.890597 342,672.36775 342,677.890597 L342,697.890597 Z M342,757.890597 C342,763.413445 337.522847,767.890597 332,767.890597 C326.477153,767.890597 322,763.413445 322,757.890597 L322,737.890597 C322,732.36775 326.477153,727.890597 332,727.890597 C337.522847,727.890597 342,732.36775 342,737.890597 L342,757.890597 Z M342,817.890597 C342,823.413445 337.522847,827.890597 332,827.890597 C326.477153,827.890597 322,823.413445 322,817.890597 L322,797.890597 C322,792.36775 326.477153,787.890597 332,787.890597 C337.522847,787.890597 342,792.36775 342,797.890597 L342,817.890597 Z M335.582572,881.313533 C333.267558,886.327768 327.326028,888.515919 322.311793,886.200905 C317.38401,883.925805 315.1857,878.148163 317.308686,873.190286 L317.424421,872.930126 C319.641243,868.128569 321.082691,862.994404 321.682313,857.675759 L321.762239,856.914714 C322.297205,851.417837 327.18698,847.395414 332.683857,847.93038 C338.180734,848.465346 342.203157,853.355121 341.668191,858.851998 C340.906019,866.683456 338.840608,874.256748 335.582572,881.313533 Z M281.638325,921.340659 C276.167253,922.095123 271.120459,918.271556 270.365996,912.800484 C269.624319,907.422142 273.306801,902.453818 278.629414,901.570197 L278.90617,901.528155 C284.457635,900.762605 289.790584,899.075198 294.728948,896.549618 C299.646067,894.034903 305.670758,895.982436 308.185473,900.899554 C310.700188,905.816673 308.752655,911.841364 303.835537,914.356079 C296.909177,917.898361 289.422579,920.267206 281.638325,921.340659 Z M220.218805,922 C214.695958,922 210.218805,917.522847 210.218805,912 C210.218805,906.477153 214.695958,902 220.218805,902 L240.218805,902 C245.741653,902 250.218805,906.477153 250.218805,912 C250.218805,917.522847 245.741653,922 240.218805,922 L220.218805,922 Z M158.334497,920.664848 C152.916888,919.591839 149.394893,914.330153 150.467902,908.912544 C151.540911,903.494934 156.802597,899.972939 162.220207,901.045948 C165.413744,901.678458 168.683351,902 172,902 L180.218805,902 C185.741653,902 190.218805,906.477153 190.218805,912 C190.218805,917.522847 185.741653,922 180.218805,922 L172,922 C167.377019,922 162.806019,921.550476 158.334497,920.664848 Z M106.788592,877.489713 C104.776845,872.346298 107.315564,866.545893 112.458979,864.534146 C117.513715,862.557085 123.20298,864.974929 125.307134,869.940827 L125.414546,870.204533 C127.444377,875.394185 130.335633,880.206211 133.955544,884.445868 C137.541718,888.646011 137.043997,894.958068 132.843853,898.544242 C128.64371,902.130415 122.331653,901.632694 118.74548,897.432551 C113.682413,891.502664 109.634218,884.7651 106.788592,877.489713 Z M102,814.328208 C102,808.805361 106.477153,804.328208 112,804.328208 C117.522847,804.328208 122,808.805361 122,814.328208 L122,834.328208 C122,839.851056 117.522847,844.328208 112,844.328208 C106.477153,844.328208 102,839.851056 102,834.328208 L102,814.328208 Z M102,754.328208 C102,748.805361 106.477153,744.328208 112,744.328208 C117.522847,744.328208 122,748.805361 122,754.328208 L122,774.328208 C122,779.851056 117.522847,784.328208 112,784.328208 C106.477153,784.328208 102,779.851056 102,774.328208 L102,754.328208 Z M102,694.328208 C102,688.805361 106.477153,684.328208 112,684.328208 C117.522847,684.328208 122,688.805361 122,694.328208 L122,714.328208 C122,719.851056 117.522847,724.328208 112,724.328208 C106.477153,724.328208 102,719.851056 102,714.328208 L102,694.328208 Z M102,634.328208 C102,628.805361 106.477153,624.328208 112,624.328208 C117.522847,624.328208 122,628.805361 122,634.328208 L122,654.328208 C122,659.851056 117.522847,664.328208 112,664.328208 C106.477153,664.328208 102,659.851056 102,654.328208 L102,634.328208 Z M102,574.328208 C102,568.805361 106.477153,564.328208 112,564.328208 C117.522847,564.328208 122,568.805361 122,574.328208 L122,594.328208 C122,599.851056 117.522847,604.328208 112,604.328208 C106.477153,604.328208 102,599.851056 102,594.328208 L102,574.328208 Z M102,514.328208 C102,508.805361 106.477153,504.328208 112,504.328208 C117.522847,504.328208 122,508.805361 122,514.328208 L122,534.328208 C122,539.851056 117.522847,544.328208 112,544.328208 C106.477153,544.328208 102,539.851056 102,534.328208 L102,514.328208 Z M102,454.328208 C102,448.805361 106.477153,444.328208 112,444.328208 C117.522847,444.328208 122,448.805361 122,454.328208 L122,474.328208 C122,479.851056 117.522847,484.328208 112,484.328208 C106.477153,484.328208 102,479.851056 102,474.328208 L102,454.328208 Z M102,394.328208 C102,388.805361 106.477153,384.328208 112,384.328208 C117.522847,384.328208 122,388.805361 122,394.328208 L122,414.328208 C122,419.851056 117.522847,424.328208 112,424.328208 C106.477153,424.328208 102,419.851056 102,414.328208 L102,394.328208 Z M102,334.328208 C102,328.805361 106.477153,324.328208 112,324.328208 C117.522847,324.328208 122,328.805361 122,334.328208 L122,354.328208 C122,359.851056 117.522847,364.328208 112,364.328208 C106.477153,364.328208 102,359.851056 102,354.328208 L102,334.328208 Z M102,274.328208 C102,268.805361 106.477153,264.328208 112,264.328208 C117.522847,264.328208 122,268.805361 122,274.328208 L122,294.328208 C122,299.851056 117.522847,304.328208 112,304.328208 C106.477153,304.328208 102,299.851056 102,294.328208 L102,274.328208 Z M102,214.328208 C102,208.805361 106.477153,204.328208 112,204.328208 C117.522847,204.328208 122,208.805361 122,214.328208 L122,234.328208 C122,239.851056 117.522847,244.328208 112,244.328208 C106.477153,244.328208 102,239.851056 102,234.328208 L102,214.328208 Z M105.003353,151.661096 C106.606034,146.375904 112.189758,143.390638 117.474951,144.993319 C122.670564,146.568836 125.643552,151.991589 124.220277,157.195942 L124.142728,157.464917 C122.727567,162.131721 122,167.011384 122,172 L122,174.328208 C122,179.851056 117.522847,184.328208 112,184.328208 C106.477153,184.328208 102,179.851056 102,174.328208 L102,172 C102,165.03958 103.019114,158.20456 105.003353,151.661096 Z M153.077691,104.590148 C158.395706,103.099992 163.914819,106.203084 165.404975,111.521099 C166.869874,116.748978 163.895909,122.171196 158.742512,123.76916 L158.474024,123.848383 C153.345681,125.285392 148.496997,127.536467 144.09376,130.504765 L143.467775,130.933676 C138.935521,134.089705 132.702936,132.974054 129.546907,128.4418 C126.390878,123.909546 127.506528,117.676962 132.038782,114.520933 C138.435844,110.066347 145.539059,106.702542 153.077691,104.590148 Z M215.562389,102 C221.085237,102 225.562389,106.477153 225.562389,112 C225.562389,117.522847 221.085237,122 215.562389,122 L195.562389,122 C190.039542,122 185.562389,117.522847 185.562389,112 C185.562389,106.477153 190.039542,102 195.562389,102 L215.562389,102 Z M272,102 C277.522847,102 282,106.477153 282,112 C282,117.522847 277.522847,122 272,122 L255.562389,122 C250.039542,122 245.562389,117.522847 245.562389,112 C245.562389,106.477153 250.039542,102 255.562389,102 L272,102 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M584.928581,105.844361 C590.146969,107.652751 592.911317,113.349081 591.102927,118.567468 C589.325715,123.695883 583.793476,126.454152 578.65014,124.831284 L578.379819,124.741813 C573.16358,122.934167 567.652303,122 562,122 C556.477153,122 552,117.522847 552,112 C552,106.477153 556.477153,102 562,102 C569.888963,102 577.613158,103.309259 584.928581,105.844361 Z M630.088269,155.693363 C631.370742,161.065244 628.055622,166.459664 622.683741,167.742137 C617.31186,169.02461 611.91744,165.70949 610.634967,160.337609 C609.338201,154.905862 607.138154,149.754737 604.137952,145.071652 C601.158708,140.421281 602.513424,134.236256 607.163795,131.257012 C611.814166,128.277768 617.999191,129.632484 620.978435,134.282855 C625.182,140.844298 628.268511,148.070963 630.088269,155.693363 Z M632,217.890597 C632,223.413445 627.522847,227.890597 622,227.890597 C616.477153,227.890597 612,223.413445 612,217.890597 L612,197.890597 C612,192.36775 616.477153,187.890597 622,187.890597 C627.522847,187.890597 632,192.36775 632,197.890597 L632,217.890597 Z M632,277.890597 C632,283.413445 627.522847,287.890597 622,287.890597 C616.477153,287.890597 612,283.413445 612,277.890597 L612,257.890597 C612,252.36775 616.477153,247.890597 622,247.890597 C627.522847,247.890597 632,252.36775 632,257.890597 L632,277.890597 Z M632,337.890597 C632,343.413445 627.522847,347.890597 622,347.890597 C616.477153,347.890597 612,343.413445 612,337.890597 L612,317.890597 C612,312.36775 616.477153,307.890597 622,307.890597 C627.522847,307.890597 632,312.36775 632,317.890597 L632,337.890597 Z M632,397.890597 C632,403.413445 627.522847,407.890597 622,407.890597 C616.477153,407.890597 612,403.413445 612,397.890597 L612,377.890597 C612,372.36775 616.477153,367.890597 622,367.890597 C627.522847,367.890597 632,372.36775 632,377.890597 L632,397.890597 Z M632,457.890597 C632,463.413445 627.522847,467.890597 622,467.890597 C616.477153,467.890597 612,463.413445 612,457.890597 L612,437.890597 C612,432.36775 616.477153,427.890597 622,427.890597 C627.522847,427.890597 632,432.36775 632,437.890597 L632,457.890597 Z M632,517.890597 C632,523.413445 627.522847,527.890597 622,527.890597 C616.477153,527.890597 612,523.413445 612,517.890597 L612,497.890597 C612,492.36775 616.477153,487.890597 622,487.890597 C627.522847,487.890597 632,492.36775 632,497.890597 L632,517.890597 Z M632,577.890597 C632,583.413445 627.522847,587.890597 622,587.890597 C616.477153,587.890597 612,583.413445 612,577.890597 L612,557.890597 C612,552.36775 616.477153,547.890597 622,547.890597 C627.522847,547.890597 632,552.36775 632,557.890597 L632,577.890597 Z M632,637.890597 C632,643.413445 627.522847,647.890597 622,647.890597 C616.477153,647.890597 612,643.413445 612,637.890597 L612,617.890597 C612,612.36775 616.477153,607.890597 622,607.890597 C627.522847,607.890597 632,612.36775 632,617.890597 L632,637.890597 Z M632,697.890597 C632,703.413445 627.522847,707.890597 622,707.890597 C616.477153,707.890597 612,703.413445 612,697.890597 L612,677.890597 C612,672.36775 616.477153,667.890597 622,667.890597 C627.522847,667.890597 632,672.36775 632,677.890597 L632,697.890597 Z M632,757.890597 C632,763.413445 627.522847,767.890597 622,767.890597 C616.477153,767.890597 612,763.413445 612,757.890597 L612,737.890597 C612,732.36775 616.477153,727.890597 622,727.890597 C627.522847,727.890597 632,732.36775 632,737.890597 L632,757.890597 Z M632,817.890597 C632,823.413445 627.522847,827.890597 622,827.890597 C616.477153,827.890597 612,823.413445 612,817.890597 L612,797.890597 C612,792.36775 616.477153,787.890597 622,787.890597 C627.522847,787.890597 632,792.36775 632,797.890597 L632,817.890597 Z M625.582572,881.313533 C623.267558,886.327768 617.326028,888.515919 612.311793,886.200905 C607.38401,883.925805 605.1857,878.148163 607.308686,873.190286 L607.424421,872.930126 C609.641243,868.128569 611.082691,862.994404 611.682313,857.675759 L611.762239,856.914714 C612.297205,851.417837 617.18698,847.395414 622.683857,847.93038 C628.180734,848.465346 632.203157,853.355121 631.668191,858.851998 C630.906019,866.683456 628.840608,874.256748 625.582572,881.313533 Z M571.638325,921.340659 C566.167253,922.095123 561.120459,918.271556 560.365996,912.800484 C559.624319,907.422142 563.306801,902.453818 568.629414,901.570197 L568.90617,901.528155 C574.457635,900.762605 579.790584,899.075198 584.728948,896.549618 C589.646067,894.034903 595.670758,895.982436 598.185473,900.899554 C600.700188,905.816673 598.752655,911.841364 593.835537,914.356079 C586.909177,917.898361 579.422579,920.267206 571.638325,921.340659 Z M510.218805,922 C504.695958,922 500.218805,917.522847 500.218805,912 C500.218805,906.477153 504.695958,902 510.218805,902 L530.218805,902 C535.741653,902 540.218805,906.477153 540.218805,912 C540.218805,917.522847 535.741653,922 530.218805,922 L510.218805,922 Z M448.334497,920.664848 C442.916888,919.591839 439.394893,914.330153 440.467902,908.912544 C441.540911,903.494934 446.802597,899.972939 452.220207,901.045948 C455.413744,901.678458 458.683351,902 462,902 L470.218805,902 C475.741653,902 480.218805,906.477153 480.218805,912 C480.218805,917.522847 475.741653,922 470.218805,922 L462,922 C457.377019,922 452.806019,921.550476 448.334497,920.664848 Z M396.788592,877.489713 C394.776845,872.346298 397.315564,866.545893 402.458979,864.534146 C407.513715,862.557085 413.20298,864.974929 415.307134,869.940827 L415.414546,870.204533 C417.444377,875.394185 420.335633,880.206211 423.955544,884.445868 C427.541718,888.646011 427.043997,894.958068 422.843853,898.544242 C418.64371,902.130415 412.331653,901.632694 408.74548,897.432551 C403.682413,891.502664 399.634218,884.7651 396.788592,877.489713 Z M392,814.328208 C392,808.805361 396.477153,804.328208 402,804.328208 C407.522847,804.328208 412,808.805361 412,814.328208 L412,834.328208 C412,839.851056 407.522847,844.328208 402,844.328208 C396.477153,844.328208 392,839.851056 392,834.328208 L392,814.328208 Z M392,754.328208 C392,748.805361 396.477153,744.328208 402,744.328208 C407.522847,744.328208 412,748.805361 412,754.328208 L412,774.328208 C412,779.851056 407.522847,784.328208 402,784.328208 C396.477153,784.328208 392,779.851056 392,774.328208 L392,754.328208 Z M392,694.328208 C392,688.805361 396.477153,684.328208 402,684.328208 C407.522847,684.328208 412,688.805361 412,694.328208 L412,714.328208 C412,719.851056 407.522847,724.328208 402,724.328208 C396.477153,724.328208 392,719.851056 392,714.328208 L392,694.328208 Z M392,634.328208 C392,628.805361 396.477153,624.328208 402,624.328208 C407.522847,624.328208 412,628.805361 412,634.328208 L412,654.328208 C412,659.851056 407.522847,664.328208 402,664.328208 C396.477153,664.328208 392,659.851056 392,654.328208 L392,634.328208 Z M392,574.328208 C392,568.805361 396.477153,564.328208 402,564.328208 C407.522847,564.328208 412,568.805361 412,574.328208 L412,594.328208 C412,599.851056 407.522847,604.328208 402,604.328208 C396.477153,604.328208 392,599.851056 392,594.328208 L392,574.328208 Z M392,514.328208 C392,508.805361 396.477153,504.328208 402,504.328208 C407.522847,504.328208 412,508.805361 412,514.328208 L412,534.328208 C412,539.851056 407.522847,544.328208 402,544.328208 C396.477153,544.328208 392,539.851056 392,534.328208 L392,514.328208 Z M392,454.328208 C392,448.805361 396.477153,444.328208 402,444.328208 C407.522847,444.328208 412,448.805361 412,454.328208 L412,474.328208 C412,479.851056 407.522847,484.328208 402,484.328208 C396.477153,484.328208 392,479.851056 392,474.328208 L392,454.328208 Z M392,394.328208 C392,388.805361 396.477153,384.328208 402,384.328208 C407.522847,384.328208 412,388.805361 412,394.328208 L412,414.328208 C412,419.851056 407.522847,424.328208 402,424.328208 C396.477153,424.328208 392,419.851056 392,414.328208 L392,394.328208 Z M392,334.328208 C392,328.805361 396.477153,324.328208 402,324.328208 C407.522847,324.328208 412,328.805361 412,334.328208 L412,354.328208 C412,359.851056 407.522847,364.328208 402,364.328208 C396.477153,364.328208 392,359.851056 392,354.328208 L392,334.328208 Z M392,274.328208 C392,268.805361 396.477153,264.328208 402,264.328208 C407.522847,264.328208 412,268.805361 412,274.328208 L412,294.328208 C412,299.851056 407.522847,304.328208 402,304.328208 C396.477153,304.328208 392,299.851056 392,294.328208 L392,274.328208 Z M392,214.328208 C392,208.805361 396.477153,204.328208 402,204.328208 C407.522847,204.328208 412,208.805361 412,214.328208 L412,234.328208 C412,239.851056 407.522847,244.328208 402,244.328208 C396.477153,244.328208 392,239.851056 392,234.328208 L392,214.328208 Z M395.003353,151.661096 C396.606034,146.375904 402.189758,143.390638 407.474951,144.993319 C412.670564,146.568836 415.643552,151.991589 414.220277,157.195942 L414.142728,157.464917 C412.727567,162.131721 412,167.011384 412,172 L412,174.328208 C412,179.851056 407.522847,184.328208 402,184.328208 C396.477153,184.328208 392,179.851056 392,174.328208 L392,172 C392,165.03958 393.019114,158.20456 395.003353,151.661096 Z M443.077691,104.590148 C448.395706,103.099992 453.914819,106.203084 455.404975,111.521099 C456.869874,116.748978 453.895909,122.171196 448.742512,123.76916 L448.474024,123.848383 C443.345681,125.285392 438.496997,127.536467 434.09376,130.504765 L433.467775,130.933676 C428.935521,134.089705 422.702936,132.974054 419.546907,128.4418 C416.390878,123.909546 417.506528,117.676962 422.038782,114.520933 C428.435844,110.066347 435.539059,106.702542 443.077691,104.590148 Z M505.562389,102 C511.085237,102 515.562389,106.477153 515.562389,112 C515.562389,117.522847 511.085237,122 505.562389,122 L485.562389,122 C480.039542,122 475.562389,117.522847 475.562389,112 C475.562389,106.477153 480.039542,102 485.562389,102 L505.562389,102 Z M562,102 C567.522847,102 572,106.477153 572,112 C572,117.522847 567.522847,122 562,122 L545.562389,122 C540.039542,122 535.562389,117.522847 535.562389,112 C535.562389,106.477153 540.039542,102 545.562389,102 L562,102 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M874.928581,105.844361 C880.146969,107.652751 882.911317,113.349081 881.102927,118.567468 C879.325715,123.695883 873.793476,126.454152 868.65014,124.831284 L868.379819,124.741813 C863.16358,122.934167 857.652303,122 852,122 C846.477153,122 842,117.522847 842,112 C842,106.477153 846.477153,102 852,102 C859.888963,102 867.613158,103.309259 874.928581,105.844361 Z M920.088269,155.693363 C921.370742,161.065244 918.055622,166.459664 912.683741,167.742137 C907.31186,169.02461 901.91744,165.70949 900.634967,160.337609 C899.338201,154.905862 897.138154,149.754737 894.137952,145.071652 C891.158708,140.421281 892.513424,134.236256 897.163795,131.257012 C901.814166,128.277768 907.999191,129.632484 910.978435,134.282855 C915.182,140.844298 918.268511,148.070963 920.088269,155.693363 Z M922,217.890597 C922,223.413445 917.522847,227.890597 912,227.890597 C906.477153,227.890597 902,223.413445 902,217.890597 L902,197.890597 C902,192.36775 906.477153,187.890597 912,187.890597 C917.522847,187.890597 922,192.36775 922,197.890597 L922,217.890597 Z M922,277.890597 C922,283.413445 917.522847,287.890597 912,287.890597 C906.477153,287.890597 902,283.413445 902,277.890597 L902,257.890597 C902,252.36775 906.477153,247.890597 912,247.890597 C917.522847,247.890597 922,252.36775 922,257.890597 L922,277.890597 Z M922,337.890597 C922,343.413445 917.522847,347.890597 912,347.890597 C906.477153,347.890597 902,343.413445 902,337.890597 L902,317.890597 C902,312.36775 906.477153,307.890597 912,307.890597 C917.522847,307.890597 922,312.36775 922,317.890597 L922,337.890597 Z M922,397.890597 C922,403.413445 917.522847,407.890597 912,407.890597 C906.477153,407.890597 902,403.413445 902,397.890597 L902,377.890597 C902,372.36775 906.477153,367.890597 912,367.890597 C917.522847,367.890597 922,372.36775 922,377.890597 L922,397.890597 Z M922,457.890597 C922,463.413445 917.522847,467.890597 912,467.890597 C906.477153,467.890597 902,463.413445 902,457.890597 L902,437.890597 C902,432.36775 906.477153,427.890597 912,427.890597 C917.522847,427.890597 922,432.36775 922,437.890597 L922,457.890597 Z M922,517.890597 C922,523.413445 917.522847,527.890597 912,527.890597 C906.477153,527.890597 902,523.413445 902,517.890597 L902,497.890597 C902,492.36775 906.477153,487.890597 912,487.890597 C917.522847,487.890597 922,492.36775 922,497.890597 L922,517.890597 Z M922,577.890597 C922,583.413445 917.522847,587.890597 912,587.890597 C906.477153,587.890597 902,583.413445 902,577.890597 L902,557.890597 C902,552.36775 906.477153,547.890597 912,547.890597 C917.522847,547.890597 922,552.36775 922,557.890597 L922,577.890597 Z M922,637.890597 C922,643.413445 917.522847,647.890597 912,647.890597 C906.477153,647.890597 902,643.413445 902,637.890597 L902,617.890597 C902,612.36775 906.477153,607.890597 912,607.890597 C917.522847,607.890597 922,612.36775 922,617.890597 L922,637.890597 Z M922,697.890597 C922,703.413445 917.522847,707.890597 912,707.890597 C906.477153,707.890597 902,703.413445 902,697.890597 L902,677.890597 C902,672.36775 906.477153,667.890597 912,667.890597 C917.522847,667.890597 922,672.36775 922,677.890597 L922,697.890597 Z M922,757.890597 C922,763.413445 917.522847,767.890597 912,767.890597 C906.477153,767.890597 902,763.413445 902,757.890597 L902,737.890597 C902,732.36775 906.477153,727.890597 912,727.890597 C917.522847,727.890597 922,732.36775 922,737.890597 L922,757.890597 Z M922,817.890597 C922,823.413445 917.522847,827.890597 912,827.890597 C906.477153,827.890597 902,823.413445 902,817.890597 L902,797.890597 C902,792.36775 906.477153,787.890597 912,787.890597 C917.522847,787.890597 922,792.36775 922,797.890597 L922,817.890597 Z M915.582572,881.313533 C913.267558,886.327768 907.326028,888.515919 902.311793,886.200905 C897.38401,883.925805 895.1857,878.148163 897.308686,873.190286 L897.424421,872.930126 C899.641243,868.128569 901.082691,862.994404 901.682313,857.675759 L901.762239,856.914714 C902.297205,851.417837 907.18698,847.395414 912.683857,847.93038 C918.180734,848.465346 922.203157,853.355121 921.668191,858.851998 C920.906019,866.683456 918.840608,874.256748 915.582572,881.313533 Z M861.638325,921.340659 C856.167253,922.095123 851.120459,918.271556 850.365996,912.800484 C849.624319,907.422142 853.306801,902.453818 858.629414,901.570197 L858.90617,901.528155 C864.457635,900.762605 869.790584,899.075198 874.728948,896.549618 C879.646067,894.034903 885.670758,895.982436 888.185473,900.899554 C890.700188,905.816673 888.752655,911.841364 883.835537,914.356079 C876.909177,917.898361 869.422579,920.267206 861.638325,921.340659 Z M800.218805,922 C794.695958,922 790.218805,917.522847 790.218805,912 C790.218805,906.477153 794.695958,902 800.218805,902 L820.218805,902 C825.741653,902 830.218805,906.477153 830.218805,912 C830.218805,917.522847 825.741653,922 820.218805,922 L800.218805,922 Z M738.334497,920.664848 C732.916888,919.591839 729.394893,914.330153 730.467902,908.912544 C731.540911,903.494934 736.802597,899.972939 742.220207,901.045948 C745.413744,901.678458 748.683351,902 752,902 L760.218805,902 C765.741653,902 770.218805,906.477153 770.218805,912 C770.218805,917.522847 765.741653,922 760.218805,922 L752,922 C747.377019,922 742.806019,921.550476 738.334497,920.664848 Z M686.788592,877.489713 C684.776845,872.346298 687.315564,866.545893 692.458979,864.534146 C697.513715,862.557085 703.20298,864.974929 705.307134,869.940827 L705.414546,870.204533 C707.444377,875.394185 710.335633,880.206211 713.955544,884.445868 C717.541718,888.646011 717.043997,894.958068 712.843853,898.544242 C708.64371,902.130415 702.331653,901.632694 698.74548,897.432551 C693.682413,891.502664 689.634218,884.7651 686.788592,877.489713 Z M682,814.328208 C682,808.805361 686.477153,804.328208 692,804.328208 C697.522847,804.328208 702,808.805361 702,814.328208 L702,834.328208 C702,839.851056 697.522847,844.328208 692,844.328208 C686.477153,844.328208 682,839.851056 682,834.328208 L682,814.328208 Z M682,754.328208 C682,748.805361 686.477153,744.328208 692,744.328208 C697.522847,744.328208 702,748.805361 702,754.328208 L702,774.328208 C702,779.851056 697.522847,784.328208 692,784.328208 C686.477153,784.328208 682,779.851056 682,774.328208 L682,754.328208 Z M682,694.328208 C682,688.805361 686.477153,684.328208 692,684.328208 C697.522847,684.328208 702,688.805361 702,694.328208 L702,714.328208 C702,719.851056 697.522847,724.328208 692,724.328208 C686.477153,724.328208 682,719.851056 682,714.328208 L682,694.328208 Z M682,634.328208 C682,628.805361 686.477153,624.328208 692,624.328208 C697.522847,624.328208 702,628.805361 702,634.328208 L702,654.328208 C702,659.851056 697.522847,664.328208 692,664.328208 C686.477153,664.328208 682,659.851056 682,654.328208 L682,634.328208 Z M682,574.328208 C682,568.805361 686.477153,564.328208 692,564.328208 C697.522847,564.328208 702,568.805361 702,574.328208 L702,594.328208 C702,599.851056 697.522847,604.328208 692,604.328208 C686.477153,604.328208 682,599.851056 682,594.328208 L682,574.328208 Z M682,514.328208 C682,508.805361 686.477153,504.328208 692,504.328208 C697.522847,504.328208 702,508.805361 702,514.328208 L702,534.328208 C702,539.851056 697.522847,544.328208 692,544.328208 C686.477153,544.328208 682,539.851056 682,534.328208 L682,514.328208 Z M682,454.328208 C682,448.805361 686.477153,444.328208 692,444.328208 C697.522847,444.328208 702,448.805361 702,454.328208 L702,474.328208 C702,479.851056 697.522847,484.328208 692,484.328208 C686.477153,484.328208 682,479.851056 682,474.328208 L682,454.328208 Z M682,394.328208 C682,388.805361 686.477153,384.328208 692,384.328208 C697.522847,384.328208 702,388.805361 702,394.328208 L702,414.328208 C702,419.851056 697.522847,424.328208 692,424.328208 C686.477153,424.328208 682,419.851056 682,414.328208 L682,394.328208 Z M682,334.328208 C682,328.805361 686.477153,324.328208 692,324.328208 C697.522847,324.328208 702,328.805361 702,334.328208 L702,354.328208 C702,359.851056 697.522847,364.328208 692,364.328208 C686.477153,364.328208 682,359.851056 682,354.328208 L682,334.328208 Z M682,274.328208 C682,268.805361 686.477153,264.328208 692,264.328208 C697.522847,264.328208 702,268.805361 702,274.328208 L702,294.328208 C702,299.851056 697.522847,304.328208 692,304.328208 C686.477153,304.328208 682,299.851056 682,294.328208 L682,274.328208 Z M682,214.328208 C682,208.805361 686.477153,204.328208 692,204.328208 C697.522847,204.328208 702,208.805361 702,214.328208 L702,234.328208 C702,239.851056 697.522847,244.328208 692,244.328208 C686.477153,244.328208 682,239.851056 682,234.328208 L682,214.328208 Z M685.003353,151.661096 C686.606034,146.375904 692.189758,143.390638 697.474951,144.993319 C702.670564,146.568836 705.643552,151.991589 704.220277,157.195942 L704.142728,157.464917 C702.727567,162.131721 702,167.011384 702,172 L702,174.328208 C702,179.851056 697.522847,184.328208 692,184.328208 C686.477153,184.328208 682,179.851056 682,174.328208 L682,172 C682,165.03958 683.019114,158.20456 685.003353,151.661096 Z M733.077691,104.590148 C738.395706,103.099992 743.914819,106.203084 745.404975,111.521099 C746.869874,116.748978 743.895909,122.171196 738.742512,123.76916 L738.474024,123.848383 C733.345681,125.285392 728.496997,127.536467 724.09376,130.504765 L723.467775,130.933676 C718.935521,134.089705 712.702936,132.974054 709.546907,128.4418 C706.390878,123.909546 707.506528,117.676962 712.038782,114.520933 C718.435844,110.066347 725.539059,106.702542 733.077691,104.590148 Z M795.562389,102 C801.085237,102 805.562389,106.477153 805.562389,112 C805.562389,117.522847 801.085237,122 795.562389,122 L775.562389,122 C770.039542,122 765.562389,117.522847 765.562389,112 C765.562389,106.477153 770.039542,102 775.562389,102 L795.562389,102 Z M852,102 C857.522847,102 862,106.477153 862,112 C862,117.522847 857.522847,122 852,122 L835.562389,122 C830.039542,122 825.562389,117.522847 825.562389,112 C825.562389,106.477153 830.039542,102 835.562389,102 L852,102 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
  </g>
)

export const TabSource = {
  light: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <rect
        stroke="#979797"
        fill="#D8D8D8"
        x="325"
        y="84"
        width="285"
        height="140"
      ></rect>
      <rect
        stroke="#979797"
        fill="#D8D8D8"
        x="630"
        y="84"
        width="285"
        height="140"
      ></rect>
      <path
        d="M944,224 C988.18278,224 1024,259.81722 1024,304 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,224 L944,224 Z M20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1003.99196 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,304 C1004,271.194286 977.671657,244.537886 944.99221,244.008038 L944,244 L20,244 L20,944 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <path
        d="M265,20 C298.137085,20 325,46.862915 325,80 L325,244 L0,244 L0,80 C0,46.862915 26.862915,20 60,20 L265,20 Z M265,40 L60,40 C38.1295239,40 20.3585905,57.5522287 20.0053589,79.3385269 L20,80 L20,224 L305,224 L305,80 C305,58.1295239 287.447771,40.3585905 265.661473,40.0053589 L265,40 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M570,64 C603.137085,64 630,90.862915 630,124 L630,244 L305,244 L305,124 C305,90.862915 331.862915,64 365,64 L570,64 Z M570,84 L365,84 C343.129524,84 325.35859,101.552229 325.005359,123.338527 L325,124 L325,224 L610,224 L610,124 C610,102.129524 592.447771,84.3585905 570.661473,84.0053589 L570,84 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M875,64 C908.137085,64 935,90.862915 935,124 L935,244 L610,244 L610,124 C610,90.862915 636.862915,64 670,64 L875,64 Z M875,84 L670,84 C648.129524,84 630.35859,101.552229 630.005359,123.338527 L630,124 L630,224 L915,224 L915,124 C915,102.129524 897.447771,84.3585905 875.661473,84.0053589 L875,84 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(162.500000, 132.000000) scale(-1, 1) rotate(630.000000) translate(-162.500000, -132.000000) "
        x="152.5"
        y="32"
        width="20"
        height="200"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(467.500000, 154.000000) scale(-1, 1) rotate(630.000000) translate(-467.500000, -154.000000) "
        x="457.5"
        y="54"
        width="20"
        height="200"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(772.500000, 154.000000) scale(-1, 1) rotate(630.000000) translate(-772.500000, -154.000000) "
        x="762.5"
        y="54"
        width="20"
        height="200"
        rx="10"
      ></rect>
    </g>
  ),
  dark: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <rect
        stroke="#979797"
        fill="#4D4D4D"
        x="325"
        y="84"
        width="285"
        height="140"
      ></rect>
      <rect
        stroke="#979797"
        fill="#4D4D4D"
        x="630"
        y="84"
        width="285"
        height="140"
      ></rect>
      <path
        d="M944,224 C988.18278,224 1024,259.81722 1024,304 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,224 L944,224 Z M20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1003.99196 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,304 C1004,271.194286 977.671657,244.537886 944.99221,244.008038 L944,244 L20,244 L20,944 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <path
        d="M265,20 C298.137085,20 325,46.862915 325,80 L325,244 L0,244 L0,80 C0,46.862915 26.862915,20 60,20 L265,20 Z M265,40 L60,40 C38.1295239,40 20.3585905,57.5522287 20.0053589,79.3385269 L20,80 L20,224 L305,224 L305,80 C305,58.1295239 287.447771,40.3585905 265.661473,40.0053589 L265,40 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M570,64 C603.137085,64 630,90.862915 630,124 L630,244 L305,244 L305,124 C305,90.862915 331.862915,64 365,64 L570,64 Z M570,84 L365,84 C343.129524,84 325.35859,101.552229 325.005359,123.338527 L325,124 L325,224 L610,224 L610,124 C610,102.129524 592.447771,84.3585905 570.661473,84.0053589 L570,84 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M875,64 C908.137085,64 935,90.862915 935,124 L935,244 L610,244 L610,124 C610,90.862915 636.862915,64 670,64 L875,64 Z M875,84 L670,84 C648.129524,84 630.35859,101.552229 630.005359,123.338527 L630,124 L630,224 L915,224 L915,124 C915,102.129524 897.447771,84.3585905 875.661473,84.0053589 L875,84 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(162.500000, 132.000000) scale(-1, 1) rotate(630.000000) translate(-162.500000, -132.000000) "
        x="152.5"
        y="32"
        width="20"
        height="200"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(467.500000, 154.000000) scale(-1, 1) rotate(630.000000) translate(-467.500000, -154.000000) "
        x="457.5"
        y="54"
        width="20"
        height="200"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(772.500000, 154.000000) scale(-1, 1) rotate(630.000000) translate(-772.500000, -154.000000) "
        x="762.5"
        y="54"
        width="20"
        height="200"
        rx="10"
      ></rect>
    </g>
  ),
}

export const CollapseSource = {
  light: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <path
        d="M65,17 L962,17 C990.718807,17 1014,40.281193 1014,69 L1014,237 L1014,237 L13,237 L13,69 C13,40.281193 36.281193,17 65,17 Z"
        fill="#D8D8D8"
      ></path>
      <rect fill="#D8D8D8" x="13" y="517" width="1001" height="220"></rect>
      <path
        d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="#999999"
        transform="translate(513.500000, 237.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -237.000000) "
        x="503.5"
        y="-263.5"
        width="20"
        height="1001"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(340.000000, 127.000000) scale(-1, 1) rotate(630.000000) translate(-340.000000, -127.000000) "
        x="330"
        y="-123"
        width="20"
        height="500"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(824.000000, 127.000000) scale(-1, 1) rotate(630.000000) translate(-824.000000, -127.000000) "
        x="814"
        y="27"
        width="20"
        height="200"
        rx="10"
      ></rect>
      <path
        d="M1014,727 L1014,747 L13,747 L13,727 L1014,727 Z M1014,507 L1014,527 L13,527 L13,507 L1014,507 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(340.000000, 627.000000) scale(-1, 1) rotate(630.000000) translate(-340.000000, -627.000000) "
        x="330"
        y="377"
        width="20"
        height="500"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(824.000000, 627.000000) scale(-1, 1) rotate(630.000000) translate(-824.000000, -627.000000) "
        x="814"
        y="527"
        width="20"
        height="200"
        rx="10"
      ></rect>
    </g>
  ),
  dark: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <path
        d="M65,17 L962,17 C990.718807,17 1014,40.281193 1014,69 L1014,237 L1014,237 L13,237 L13,69 C13,40.281193 36.281193,17 65,17 Z"
        fill="#4D4D4D"
      ></path>
      <rect fill="#4D4D4D" x="13" y="517" width="1001" height="220"></rect>
      <path
        d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="#999999"
        transform="translate(513.500000, 237.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -237.000000) "
        x="503.5"
        y="-263.5"
        width="20"
        height="1001"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(340.000000, 127.000000) scale(-1, 1) rotate(630.000000) translate(-340.000000, -127.000000) "
        x="330"
        y="-123"
        width="20"
        height="500"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(824.000000, 127.000000) scale(-1, 1) rotate(630.000000) translate(-824.000000, -127.000000) "
        x="814"
        y="27"
        width="20"
        height="200"
        rx="10"
      ></rect>
      <path
        d="M1014,727 L1014,747 L13,747 L13,727 L1014,727 Z M1014,507 L1014,527 L13,527 L13,507 L1014,507 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(340.000000, 627.000000) scale(-1, 1) rotate(630.000000) translate(-340.000000, -627.000000) "
        x="330"
        y="377"
        width="20"
        height="500"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(824.000000, 627.000000) scale(-1, 1) rotate(630.000000) translate(-824.000000, -627.000000) "
        x="814"
        y="527"
        width="20"
        height="200"
        rx="10"
      ></rect>
    </g>
  ),
}

export const ArrayCardsSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(513.500000, 220.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -220.000000) "
      x="503.5"
      y="-280.5"
      width="20"
      height="1001"
      rx="10"
    ></rect>
    <path
      d="M511.799425,386 C384.332422,386 281,489.332422 281,616.799425 C281,744.266428 384.332422,847.598851 511.799425,847.598851 C639.266428,847.598851 742.598851,744.266428 742.598851,616.799425 C742.598851,489.332422 639.266428,386 511.799425,386 Z M511.799425,406 C628.220733,406 722.598851,500.378117 722.598851,616.799425 C722.598851,733.220733 628.220733,827.598851 511.799425,827.598851 C395.378117,827.598851 301,733.220733 301,616.799425 C301,500.378117 395.378117,406 511.799425,406 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(511.799425, 616.799425) scale(-1, 1) rotate(630.000000) translate(-511.799425, -616.799425) "
      x="501.799425"
      y="516.799425"
      width="20"
      height="200"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(511.799425, 616.799425) scale(-1, 1) translate(-511.799425, -616.799425) "
      x="501.799425"
      y="516.799425"
      width="20"
      height="200"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(340.000000, 114.944390) scale(-1, 1) rotate(630.000000) translate(-340.000000, -114.944390) "
      x="330"
      y="-135.05561"
      width="20"
      height="500"
      rx="10"
    ></rect>
  </g>
)

export const ArrayTableSource = {
  light: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <path
        d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="#999999"
        transform="translate(512.000000, 220.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -220.000000) "
        x="502"
        y="-292"
        width="20"
        height="1024"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(124.086526, 119.944390) scale(-1, 1) rotate(45.000000) translate(-124.086526, -119.944390) "
        x="114.086526"
        y="-11.5556095"
        width="20"
        height="263"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(220.000000, 512.000000) scale(-1, 1) rotate(720.000000) translate(-220.000000, -512.000000) "
        x="210"
        y="0"
        width="20"
        height="1024"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(415.666667, 512.000000) scale(-1, 1) rotate(720.000000) translate(-415.666667, -512.000000) "
        x="405.666667"
        y="9.09494702e-13"
        width="20"
        height="1024"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(611.333333, 512.000000) scale(-1, 1) rotate(720.000000) translate(-611.333333, -512.000000) "
        x="601.333333"
        y="0"
        width="20"
        height="1024"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(807.000000, 512.000000) scale(-1, 1) rotate(720.000000) translate(-807.000000, -512.000000) "
        x="797"
        y="0"
        width="20"
        height="1024"
      ></rect>
      <path
        d="M511.799425,386 C384.332422,386 281,489.332422 281,616.799425 C281,744.266428 384.332422,847.598851 511.799425,847.598851 C639.266428,847.598851 742.598851,744.266428 742.598851,616.799425 C742.598851,489.332422 639.266428,386 511.799425,386 Z M511.799425,406 C628.220733,406 722.598851,500.378117 722.598851,616.799425 C722.598851,733.220733 628.220733,827.598851 511.799425,827.598851 C395.378117,827.598851 301,733.220733 301,616.799425 C301,500.378117 395.378117,406 511.799425,406 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <circle fill="#FFFFFF" cx="511.799425" cy="616.799425" r="211"></circle>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(511.799425, 616.799425) scale(-1, 1) rotate(630.000000) translate(-511.799425, -616.799425) "
        x="501.799425"
        y="516.799425"
        width="20"
        height="200"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(511.799425, 616.799425) scale(-1, 1) translate(-511.799425, -616.799425) "
        x="501.799425"
        y="516.799425"
        width="20"
        height="200"
        rx="10"
      ></rect>
    </g>
  ),
  dark: (
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <path
        d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        fill="#999999"
        transform="translate(512.000000, 220.000000) scale(-1, 1) rotate(630.000000) translate(-512.000000, -220.000000) "
        x="502"
        y="-292"
        width="20"
        height="1024"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(124.086526, 119.944390) scale(-1, 1) rotate(45.000000) translate(-124.086526, -119.944390) "
        x="114.086526"
        y="-11.5556095"
        width="20"
        height="263"
        rx="10"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(220.000000, 512.000000) scale(-1, 1) rotate(720.000000) translate(-220.000000, -512.000000) "
        x="210"
        y="0"
        width="20"
        height="1024"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(415.666667, 512.000000) scale(-1, 1) rotate(720.000000) translate(-415.666667, -512.000000) "
        x="405.666667"
        y="9.09494702e-13"
        width="20"
        height="1024"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(611.333333, 512.000000) scale(-1, 1) rotate(720.000000) translate(-611.333333, -512.000000) "
        x="601.333333"
        y="0"
        width="20"
        height="1024"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(807.000000, 512.000000) scale(-1, 1) rotate(720.000000) translate(-807.000000, -512.000000) "
        x="797"
        y="0"
        width="20"
        height="1024"
      ></rect>
      <path
        d="M511.799425,386 C384.332422,386 281,489.332422 281,616.799425 C281,744.266428 384.332422,847.598851 511.799425,847.598851 C639.266428,847.598851 742.598851,744.266428 742.598851,616.799425 C742.598851,489.332422 639.266428,386 511.799425,386 Z M511.799425,406 C628.220733,406 722.598851,500.378117 722.598851,616.799425 C722.598851,733.220733 628.220733,827.598851 511.799425,827.598851 C395.378117,827.598851 301,733.220733 301,616.799425 C301,500.378117 395.378117,406 511.799425,406 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <circle
        fill="#222222"
        fillRule="nonzero"
        cx="511.799425"
        cy="616.799425"
        r="211"
      ></circle>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(511.799425, 616.799425) scale(-1, 1) rotate(630.000000) translate(-511.799425, -616.799425) "
        x="501.799425"
        y="516.799425"
        width="20"
        height="200"
        rx="10"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(511.799425, 616.799425) scale(-1, 1) translate(-511.799425, -616.799425) "
        x="501.799425"
        y="516.799425"
        width="20"
        height="200"
        rx="10"
      ></rect>
    </g>
  ),
}

export const ButtonSource = (
  <svg viewBox="0 0 1424 1024">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <path
        d="M369.1875,573.625 C395.020833,573.625 414.708333,568.416667 428.25,558 C441.791667,547.583333 448.5625,534.25 448.5625,518 C448.5625,505.916667 444.291667,494.770833 435.75,484.5625 C430.125,477.6875 421.270833,472.583333 409.1875,469.25 C430.4375,460.916667 441.0625,445.291667 441.0625,422.375 C441.0625,406.541667 435.229167,393.833333 423.5625,384.25 C413.5625,376.333333 400.125,372.375 383.25,372.375 L383.25,372.375 L312.3125,372.375 C303.354167,372.375 298.875,376.854167 298.875,385.8125 L298.875,385.8125 L298.875,560.1875 C298.875,569.145833 303.354167,573.625 312.3125,573.625 L312.3125,573.625 L369.1875,573.625 Z M372.3125,458.3125 L322,458.3125 L322,400.1875 C322,396.854167 323.770833,395.1875 327.3125,395.1875 L327.3125,395.1875 L389.1875,395.1875 C397.3125,395.1875 403.666667,396.958333 408.25,400.5 C414.916667,405.916667 418.25,413.208333 418.25,422.375 C418.25,434.041667 413.354167,443.208333 403.5625,449.875 C395.020833,455.5 384.604167,458.3125 372.3125,458.3125 L372.3125,458.3125 Z M377.9375,551.125 L327.3125,551.125 C323.770833,551.125 322,549.458333 322,546.125 L322,546.125 L322,481.125 L372.3125,481.125 C390.020833,481.125 403.25,484.5625 412,491.4375 C421.166667,498.9375 425.75,507.583333 425.75,517.375 C425.75,528.208333 420.4375,536.854167 409.8125,543.3125 C401.479167,548.520833 390.854167,551.125 377.9375,551.125 L377.9375,551.125 Z M593.25,578.625 C600.75,578.625 604.5,574.145833 604.5,565.1875 L604.5,565.1875 L604.5,446.125 C604.5,437.166667 600.75,432.6875 593.25,432.6875 C585.958333,432.6875 582.3125,437.166667 582.3125,446.125 L582.3125,446.125 L582.3125,518.625 C582.3125,528 575.645833,536.958333 562.3125,545.5 C551.479167,552.375 541.270833,555.8125 531.6875,555.8125 C520.645833,555.8125 512,552.6875 505.75,546.4375 C499.708333,540.395833 496.6875,531.75 496.6875,520.5 L496.6875,520.5 L496.6875,446.125 C496.6875,437.166667 492.9375,432.6875 485.4375,432.6875 C477.9375,432.6875 474.1875,437.166667 474.1875,446.125 L474.1875,446.125 L474.1875,518.9375 C474.1875,536.229167 479.708333,550.395833 490.75,561.4375 C501.791667,572.895833 515.854167,578.625 532.9375,578.625 C541.6875,578.625 550.645833,576.333333 559.8125,571.75 C569.1875,567.166667 576.6875,561.541667 582.3125,554.875 L582.3125,554.875 L582.3125,565.1875 C582.3125,574.145833 585.958333,578.625 593.25,578.625 Z M699.8125,573.625 C708.5625,573.625 712.9375,569.875 712.9375,562.375 C712.9375,554.875 708.5625,551.125 699.8125,551.125 L699.8125,551.125 L678.875,551.125 C675.75,551.125 674.1875,546.958333 674.1875,538.625 L674.1875,538.625 L674.1875,459.5625 L697.9375,459.5625 C706.6875,459.5625 711.0625,455.708333 711.0625,448 C711.0625,440.291667 706.6875,436.4375 697.9375,436.4375 L697.9375,436.4375 L674.1875,436.4375 L674.1875,411.75 C674.1875,402.791667 670.333333,398.3125 662.625,398.3125 C655.541667,398.3125 652,402.791667 652,411.75 L652,411.75 L652,436.4375 L635.125,436.4375 C626.375,436.4375 622,440.291667 622,448 C622,455.708333 626.375,459.5625 635.125,459.5625 L635.125,459.5625 L652,459.5625 L652,539.5625 C652,562.0625 659.604167,573.3125 674.8125,573.3125 C677.729167,573.3125 681.895833,573.416667 687.3125,573.625 L687.3125,573.625 L699.8125,573.625 Z M802,573.625 C810.75,573.625 815.125,569.875 815.125,562.375 C815.125,554.875 810.75,551.125 802,551.125 L802,551.125 L781.0625,551.125 C777.9375,551.125 776.375,546.958333 776.375,538.625 L776.375,538.625 L776.375,459.5625 L800.125,459.5625 C808.875,459.5625 813.25,455.708333 813.25,448 C813.25,440.291667 808.875,436.4375 800.125,436.4375 L800.125,436.4375 L776.375,436.4375 L776.375,411.75 C776.375,402.791667 772.520833,398.3125 764.8125,398.3125 C757.729167,398.3125 754.1875,402.791667 754.1875,411.75 L754.1875,411.75 L754.1875,436.4375 L737.3125,436.4375 C728.5625,436.4375 724.1875,440.291667 724.1875,448 C724.1875,455.708333 728.5625,459.5625 737.3125,459.5625 L737.3125,459.5625 L754.1875,459.5625 L754.1875,539.5625 C754.1875,562.0625 761.791667,573.3125 777,573.3125 C779.916667,573.3125 784.083333,573.416667 789.5,573.625 L789.5,573.625 L802,573.625 Z M905.75,578.3125 C926.791667,578.3125 944.291667,571.229167 958.25,557.0625 C972.416667,542.895833 979.5,525.5 979.5,504.875 C979.5,483.416667 972.520833,465.916667 958.5625,452.375 C945.020833,439.041667 927.416667,432.375 905.75,432.375 C883.875,432.375 865.958333,438.9375 852,452.0625 C838.25,465.395833 831.375,483 831.375,504.875 C831.375,526.333333 838.354167,543.9375 852.3125,557.6875 C866.479167,571.4375 884.291667,578.3125 905.75,578.3125 Z M905.75,555.5 C891.375,555.5 879.395833,550.708333 869.8125,541.125 C860.229167,531.541667 855.4375,519.458333 855.4375,504.875 C855.4375,489.458333 860.020833,477.270833 869.1875,468.3125 C878.354167,459.354167 890.541667,454.875 905.75,454.875 C920.958333,454.875 933.041667,459.458333 942,468.625 C950.75,477.583333 955.125,489.666667 955.125,504.875 C955.125,519.25 950.541667,531.333333 941.375,541.125 C932,550.708333 920.125,555.5 905.75,555.5 Z M1120.125,578.3125 C1127.625,578.3125 1131.375,573.833333 1131.375,564.875 L1131.375,564.875 L1131.375,488.3125 C1131.375,472.0625 1125.54167,458.729167 1113.875,448.3125 C1102.625,437.6875 1088.77083,432.375 1072.3125,432.375 C1052.9375,432.375 1037,439.979167 1024.5,455.1875 L1024.5,455.1875 L1024.5,445.8125 C1024.5,436.854167 1020.75,432.375 1013.25,432.375 C1005.75,432.375 1002,436.854167 1002,445.8125 L1002,445.8125 L1002,564.875 C1002,573.833333 1006.27083,578.3125 1014.8125,578.3125 C1021.27083,578.3125 1024.5,573.833333 1024.5,564.875 L1024.5,564.875 L1024.5,491.4375 C1024.5,483.3125 1028.97917,475.395833 1037.9375,467.6875 C1047.9375,459.145833 1059.39583,454.875 1072.3125,454.875 C1082.10417,454.875 1090.64583,458 1097.9375,464.25 C1105.22917,470.708333 1108.875,478.729167 1108.875,488.3125 L1108.875,488.3125 L1108.875,564.875 C1108.875,573.833333 1112.625,578.3125 1120.125,578.3125 Z"
        fill="#999999"
        fillRule="nonzero"
      ></path>
      <rect
        stroke="#999999"
        strokeWidth="20"
        x="10"
        y="228"
        width="1404"
        height="568"
        rx="80"
      ></rect>
      <rect
        fill="var(--dn-brand-color)"
        transform="translate(712.000000, 686.000000) scale(-1, 1) rotate(90.000000) translate(-712.000000, -686.000000) "
        x="702"
        y="286"
        width="20"
        height="800"
        rx="10"
      ></rect>
    </g>
  </svg>
)

export const MediaSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M760.68194,324.234056 L458.713667,385.538094 C452.938335,386.710572 447.636234,389.558058 443.469151,393.725142 C431.753422,405.44087 431.753422,424.43582 443.469151,436.151548 L684.087803,676.770201 C688.253627,680.936024 693.553811,683.783077 699.327192,684.956207 C715.563927,688.255454 731.40097,677.767548 734.700217,661.530813 L796.049836,359.608122 C796.850539,355.667586 796.850893,351.606295 796.050878,347.66562 C792.754463,331.428309 776.91925,320.937641 760.68194,324.234056 Z M776.45071,351.644744 C776.717382,352.958303 776.717264,354.312067 776.450363,355.625579 L715.100743,657.548269 C714.000994,662.960514 708.72198,666.456483 703.309735,665.356734 C701.385275,664.965691 699.618547,664.016673 698.229939,662.628065 L457.611287,422.009413 C453.706044,418.10417 453.706044,411.77252 457.611287,407.867277 C459.000314,406.478249 460.767681,405.529087 462.692792,405.138261 L764.661064,343.834223 C769.981765,342.754042 775.172938,346.115089 776.391257,351.3712 L776.45071,351.644744 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(615.666491, 504.595655) scale(-1, 1) rotate(45.000000) translate(-615.666491, -504.595655) "
    ></path>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(363.000000, 512.000000) scale(-1, 1) translate(-363.000000, -512.000000) "
      x="353"
      y="312"
      width="20"
      height="400"
      rx="10"
    ></rect>
  </g>
)

export const NotificationSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <rect
      fill="#999999"
      transform="translate(266.000000, 123.000000) scale(-1, 1) translate(-266.000000, -123.000000) "
      x="256"
      y="63"
      width="20"
      height="120"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(770.000000, 123.000000) scale(-1, 1) translate(-770.000000, -123.000000) "
      x="760"
      y="63"
      width="20"
      height="120"
      rx="10"
    ></rect>
    <path
      d="M165.894531,113.494448 C171.417379,113.494448 175.894531,117.9716 175.894531,123.494448 C175.894531,128.923687 171.56786,133.342379 166.174436,133.490606 L165.894531,133.494448 L88.4375,133.494448 C51.0375365,133.494448 20.613907,163.802329 20.0091744,201.462605 L20,202.606052 L20,934.388395 C20,972.194862 50.0496299,1002.88081 87.3063267,1003.49075 L88.4375,1003.5 L935.5625,1003.5 C972.962464,1003.5 1003.38609,973.192119 1003.99083,935.531843 L1004,934.388395 L1004,202.606052 C1004,164.799585 973.95037,134.113639 936.693673,133.503701 L935.5625,133.494448 L859.085938,133.494448 C853.56309,133.494448 849.085938,129.017295 849.085938,123.494448 C849.085938,118.065208 853.412608,113.646516 858.806033,113.494448 L859.085938,113.494448 L935.5625,113.494448 C983.93581,113.494448 1023.20756,152.615815 1023.98816,201.132989 L1024,202.606052 L1024,934.388395 C1024,983.092351 985.211983,1022.70076 937.025542,1023.48806 L935.5625,1023.5 L88.4375,1023.5 C40.0641901,1023.5 0.792440052,984.378633 0.011842382,935.861458 L0,934.388395 L0,202.606052 C0,153.902096 38.788017,114.293689 86.9744583,113.506392 L88.4375,113.494448 L165.894531,113.494448 Z M661.03125,113.494448 C666.554097,113.494448 671.03125,117.9716 671.03125,123.494448 C671.03125,128.923687 666.704579,133.342379 661.311155,133.490606 L661.03125,133.494448 L363.949219,133.494448 C358.426371,133.494448 353.949219,129.017295 353.949219,123.494448 C353.949219,118.065208 358.27589,113.646516 363.669314,113.498289 L363.949219,113.494448 L661.03125,113.494448 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M512,353.997224 C539.884857,353.997224 562.542797,376.376315 562.993167,404.153846 L563,404.997224 L543,404.997224 C543,387.876397 529.120827,373.997224 512,373.997224 C495.050381,373.997224 481.277908,387.600201 481.004153,404.484582 L481,404.997224 L461,404.997224 C461,376.830702 483.833478,353.997224 512,353.997224 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M512,784.997224 C539.884857,784.997224 562.542797,807.376315 562.993167,835.153846 L563,835.997224 L543,835.997224 C543,818.876397 529.120827,804.997224 512,804.997224 C495.050381,804.997224 481.277908,818.600201 481.004153,835.484582 L481,835.997224 L461,835.997224 C461,807.830702 483.833478,784.997224 512,784.997224 Z"
      fill="#979797"
      fillRule="nonzero"
      transform="translate(512.000000, 810.497224) scale(1, -1) translate(-512.000000, -810.497224) "
    ></path>
    <rect
      fill="#999999"
      transform="translate(512.000000, 784.997224) scale(-1, 1) rotate(630.000000) translate(-512.000000, -784.997224) "
      x="502"
      y="584.997224"
      width="20"
      height="400"
      rx="10"
    ></rect>
    <path
      d="M511.449219,394.997224 C603.031837,394.997224 677.447621,468.497181 678.926778,559.724009 L678.949219,562.493827 L679,711.997224 L679,795 L659,795 L659.000001,712.000621 L658.949219,562.497224 C658.949219,481.035223 592.911219,414.997224 511.449219,414.997224 C430.801838,414.997224 365.271521,479.721067 363.968979,560.054746 L363.949219,562.493827 L364,711.997224 L364,795 L344,795 L344.000001,712.000621 L343.949219,562.497224 C343.949219,469.989528 418.941523,394.997224 511.449219,394.997224 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
  </g>
)

export const ImageSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M340,140 C428.36556,140 500,211.63444 500,300 C500,388.36556 428.36556,460 340,460 C251.63444,460 180,388.36556 180,300 C180,211.63444 251.63444,140 340,140 Z M340,160 C262.680135,160 200,222.680135 200,300 C200,377.319865 262.680135,440 340,440 C417.319865,440 480,377.319865 480,300 C480,222.680135 417.319865,160 340,160 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M734.597206,481.106686 C757.881472,450.245168 801.775303,444.102591 832.636822,467.386857 C839.857253,472.834496 845.951313,479.628196 850.584274,487.390305 L851.014324,488.120829 L1032.6482,800.979178 L1015.3518,811.020822 L833.717925,498.162472 C830.372759,492.400534 825.909759,487.365262 820.591138,483.352492 C798.767635,466.88719 767.820583,471.022779 751.066182,492.496352 L750.562841,493.15237 L547.180849,762.719689 C546.774236,763.258623 546.359848,763.791646 545.937815,764.318592 C522.012172,794.191852 478.596414,799.263866 448.45171,775.912797 L447.54233,775.196485 L325.857503,677.738501 C324.721792,676.828904 323.546916,675.969317 322.33623,675.162193 C299.58956,659.997746 268.936391,665.931242 253.462928,688.34544 L252.998706,689.029698 L45.3205029,1000.547 L28.6794971,989.452998 L236.3577,677.935694 C257.802372,645.768686 301.263226,637.076515 333.430234,658.521187 C334.8427,659.462831 336.220352,660.455482 337.560471,661.497151 L338.360016,662.128019 L460.044843,759.586002 C481.598422,776.84837 513.064964,773.369657 530.327332,751.816079 L530.775403,751.248253 L531.215213,750.674005 L734.597206,481.106686 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
  </g>
)

export const TextSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <path
      d="M550.172056,176.958801 L294.240951,176.958801 C272.816052,176.958801 253.772297,190.608913 246.890487,210.898492 L179.876194,408.476126 C174.967796,422.947511 176.946524,438.868968 185.247913,451.698633 L419.119809,813.143935 L658.760775,451.908879 C667.319335,439.007696 669.418771,422.869559 664.445887,408.208049 L597.522521,210.898492 C590.64071,190.608913 571.596956,176.958801 550.172056,176.958801 Z M294.240951,196.958801 L550.172056,196.958801 C563.026996,196.958801 574.453249,205.148868 578.582335,217.322615 L645.505701,414.632172 L645.661487,415.104267 C648.443208,423.774276 647.138071,433.250188 642.094634,440.85267 L419.328,776.651 L202.039411,440.833754 C197.058578,433.135955 195.871341,423.583081 198.81638,414.90025 L265.830673,217.322615 C269.959759,205.148868 281.386011,196.958801 294.240951,196.958801 Z"
      fill="#979797"
      fillRule="nonzero"
      transform="translate(422.161055, 495.051368) rotate(45.000000) translate(-422.161055, -495.051368) "
    ></path>
    <rect
      fill="#999999"
      transform="translate(364.149199, 543.157628) rotate(45.000000) translate(-364.149199, -543.157628) "
      x="352.764584"
      y="333.111474"
      width="22.7692308"
      height="420.092308"
    ></rect>
    <path
      d="M703.68796,65.6188677 L706.45719,65.6188677 C711.980038,65.6188677 716.45719,70.0960202 716.45719,75.6188677 L716.45719,328.849637 C716.45719,334.372484 711.980038,338.849637 706.45719,338.849637 L703.68796,338.849637 C698.165112,338.849637 693.68796,334.372484 693.68796,328.849637 L693.68796,75.6188677 C693.68796,70.0960202 698.165112,65.6188677 703.68796,65.6188677 Z"
      fill="var(--dn-brand-color)"
      transform="translate(705.072575, 202.234252) rotate(135.000000) translate(-705.072575, -202.234252) "
    ></path>
    <circle
      fill="#999999"
      transform="translate(519.516877, 387.789950) rotate(45.000000) translate(-519.516877, -387.789950) "
      cx="519.516877"
      cy="387.78995"
      r="24.4769231"
    ></circle>
    <path
      d="M764.560664,481.577606 C768.827837,478.071458 775.129359,478.68839 778.635508,482.955563 C782.141656,487.222736 781.524724,493.524258 777.257551,497.030407 C753.992056,516.146642 733.176046,541.574627 713.702878,573.670311 L712.262861,576.059969 C711.064865,578.061523 709.871882,580.088517 708.683652,582.141034 L707.579762,584.0571 L706.480345,585.983916 L705.385141,587.921935 L704.293892,589.871611 L703.206337,591.833396 C703.025371,592.161395 702.844549,592.489917 702.663864,592.818972 L701.581364,594.799772 L700.50191,596.793814 L699.425243,598.801553 C699.246016,599.137343 699.066894,599.473722 698.887873,599.8107 L697.814898,601.839834 L696.744061,603.883796 L695.675102,605.943041 L694.607762,608.018022 L693.541781,610.109191 L692.4769,612.217004 L691.412859,614.341911 L690.349398,616.484368 L689.286259,618.644827 C689.109082,619.006429 688.931907,619.3688 688.754729,619.731949 L687.691585,621.920261 C687.514375,622.286562 687.33715,622.65366 687.159907,623.021564 L686.096175,625.238749 L685.031726,627.475749 L683.966301,629.733018 L682.89964,632.011008 C682.721746,632.392425 682.54379,632.774724 682.365766,633.157915 L681.296765,635.467838 L680.22588,637.799615 L679.152852,640.153701 L678.07742,642.530548 L676.999325,644.93061 L675.918309,647.354339 L674.290737,651.035302 L673.201279,653.520184 L672.107991,656.030321 L671.010613,658.566165 L669.908886,661.12817 L668.80255,663.716789 L667.133836,667.650611 L666.014843,670.307748 L664.325927,674.346472 L663.192655,677.074961 L661.481104,681.222836 L660.331931,684.025508 L659.175943,686.858648 L658.012882,689.722707 L656.813988,692.691714 L653.665611,700.584206 L648.57097,713.466473 L627.086022,768.085028 L625.836738,771.219108 L624.649518,774.178068 L622.893636,778.529126 L621.739037,781.372797 L620.030015,785.554841 L618.34712,789.63924 L616.688861,793.628816 L615.596304,796.237248 L614.513592,798.805626 L613.440282,801.334788 C613.262156,801.753094 613.084403,802.1698 612.907015,802.584924 L612.058308,804.565445 L610.972663,807.082142 L609.895443,809.56013 L608.826165,812.000327 L607.764344,814.403648 L606.709497,816.771008 L605.66114,819.103323 L604.618787,821.401509 C604.445536,821.781747 604.272514,822.160601 604.099712,822.53809 L603.06546,824.786801 C602.721536,825.531013 602.378411,826.269919 602.036004,827.003672 L601.010859,829.189619 L599.989542,831.345558 C599.819614,831.702432 599.649826,832.058093 599.480167,832.412562 L598.463683,834.525207 C598.2945,834.874979 598.125427,835.223596 597.956453,835.571078 L596.943713,837.642489 C596.775094,837.985503 596.606554,838.32742 596.438082,838.668258 L595.427998,840.700496 C595.091524,841.373696 594.755244,842.042735 594.419078,842.707764 L593.41084,844.690976 L592.402799,846.651049 L591.394471,848.588899 C591.226365,848.910073 591.058227,849.230359 590.890048,849.549777 L589.880381,851.456009 L588.869218,853.342307 L587.856074,855.209588 L586.840464,857.058766 L585.821906,858.890758 C585.651874,859.194708 585.481698,859.497981 585.31137,859.800595 L584.184141,861.788753 L583.055017,863.7511 C582.113255,865.375646 581.169384,866.973352 580.223073,868.544331 L579.086307,870.416684 C564.861556,893.660931 550.057783,910.902071 533.553373,922.526917 C469.480948,967.65619 388.685598,981.260906 291.747026,963.635711 C286.313262,962.647754 282.709224,957.441921 283.697181,952.008157 C284.685138,946.574394 289.890971,942.970356 295.324735,943.958313 C387.296085,960.680376 462.673427,947.987972 522.036462,906.175747 C529.428814,900.96896 536.571079,894.251691 543.567123,885.964477 L544.642298,884.677127 C547.327421,881.427746 549.991702,877.945673 552.64105,874.227526 L553.700006,872.727668 C554.405465,871.719358 555.109918,870.694229 555.813477,869.652216 L556.86816,868.076526 C557.39518,867.282341 557.921729,866.478641 558.447854,865.665399 L559.499552,864.026184 C560.025133,863.200207 560.550321,862.364669 561.075164,861.519545 L562.124403,859.816504 C563.872416,856.956771 565.616943,853.990311 567.359736,850.916123 L568.258884,849.320367 L569.010494,847.972146 L569.760798,846.613345 L570.510005,845.243564 L571.258324,843.862404 L572.379592,841.768456 L573.126603,840.357104 L573.873456,838.932978 L574.620359,837.495678 L575.741266,835.314156 L576.489209,833.842178 L577.612654,831.606769 L578.738561,829.337233 L579.867634,827.032225 L580.622457,825.475181 L581.379208,823.901374 L582.138097,822.310406 L582.899333,820.701878 L584.046042,818.255287 L584.814057,816.601113 L585.971913,814.084186 L586.748011,812.381874 L588.311211,808.916992 L589.494062,806.264035 L590.686654,803.563042 L591.487473,801.73503 L592.293144,799.884672 L593.511202,797.066363 L594.741348,794.195533 L595.984286,791.270835 L597.662654,787.285118 L598.938181,784.229495 L600.662556,780.064324 L602.415517,775.792223 L603.750002,772.516054 L605.557006,768.049147 L607.397186,763.466534 L608.507135,760.680615 L630.724898,704.204366 L636.542866,689.518364 L638.481786,684.673812 L640.086166,680.709525 L641.278886,677.779683 L643.054969,673.443648 L644.230765,670.591409 L645.982766,666.369522 L647.721569,662.213954 L648.873903,659.479618 L650.592798,655.430971 L652.301,651.444309 L653.434324,648.820167 L654.563592,646.222369 L655.689082,643.650433 L657.37084,639.839967 L659.045662,636.084981 L660.15882,633.611676 L661.269591,631.161824 L662.378253,628.734945 C662.56287,628.332352 662.74741,627.930697 662.93188,627.529968 L664.037901,625.136643 L665.142509,622.765084 L666.245981,620.41481 L667.348597,618.08534 L668.450635,615.776192 L669.552373,613.486884 L670.654088,611.216933 L671.75606,608.965859 L672.858567,606.73318 L673.961887,604.518414 C674.145857,604.150752 674.329872,603.783816 674.513939,603.417597 L675.619,601.228796 L676.725569,599.056704 L677.833926,596.900838 L678.944348,594.760717 L680.057113,592.635858 L681.1725,590.525781 L682.290787,588.430004 L683.412252,586.348044 L684.537174,584.279419 L685.66583,582.223649 L686.7985,580.180251 L687.935461,578.148743 L689.076991,576.128644 L690.223369,574.119472 L691.374874,572.120744 C713.399942,534.075238 737.233372,504.031241 764.560664,481.577606 Z"
      fill="#979797"
      fillRule="nonzero"
      transform="translate(532.221771, 725.196578) rotate(45.000000) translate(-532.221771, -725.196578) "
    ></path>
  </g>
)

export const CreateButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M512,322 C407.065898,322 322,407.065898 322,512 C322,616.934102 407.065898,702 512,702 C616.934102,702 702,616.934102 702,512 C702,407.065898 616.934102,322 512,322 Z M512,342 C605.888407,342 682,418.111593 682,512 C682,605.888407 605.888407,682 512,682 C418.111593,682 342,605.888407 342,512 C342,418.111593 418.111593,342 512,342 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(511.799425, 511.799425) scale(-1, 1) rotate(630.000000) translate(-511.799425, -511.799425) "
      x="501.799425"
      y="411.799425"
      width="20"
      height="200"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(511.799425, 511.799425) scale(-1, 1) translate(-511.799425, -511.799425) "
      x="501.799425"
      y="411.799425"
      width="20"
      height="200"
      rx="10"
    ></rect>
  </g>
)

export const DeleteButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M512,322 C407.065898,322 322,407.065898 322,512 C322,616.934102 407.065898,702 512,702 C616.934102,702 702,616.934102 702,512 C702,407.065898 616.934102,322 512,322 Z M512,342 C605.888407,342 682,418.111593 682,512 C682,605.888407 605.888407,682 512,682 C418.111593,682 342,605.888407 342,512 C342,418.111593 418.111593,342 512,342 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <g
      transform="translate(511.799425, 511.799425) rotate(45.000000) translate(-511.799425, -511.799425) translate(411.799425, 411.799425)"
      fill="var(--dn-brand-color)"
    >
      <rect
        transform="translate(100.000000, 100.000000) scale(-1, 1) rotate(630.000000) translate(-100.000000, -100.000000) "
        x="90"
        y="0"
        width="20"
        height="200"
        rx="10"
      ></rect>
      <rect
        transform="translate(100.000000, 100.000000) scale(-1, 1) translate(-100.000000, -100.000000) "
        x="90"
        y="0"
        width="20"
        height="200"
        rx="10"
      ></rect>
    </g>
  </g>
)

export const SubmitButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M658.502514,333.621244 L317.482641,448.99623 C308.065853,452.18215 303.014736,462.398662 306.200656,471.81545 C307.662804,476.137198 310.711524,479.742469 314.730328,481.902234 L460.237096,560.099749 L537.160002,704.08365 C541.844412,712.851904 552.749951,716.162514 561.518205,711.478105 C565.634183,709.279157 568.735305,705.56766 570.167564,701.126345 L681.402364,356.196416 C684.453499,346.735097 679.257019,336.591749 669.7957,333.540614 C666.120129,332.355296 662.160785,332.383565 658.502514,333.621244 Z M661.147,353.84 L552.579,690.501 L477.877462,550.675445 L477.658795,550.277097 C475.800449,546.981033 473.04329,544.276793 469.704796,542.482637 L328.255,466.465 L661.147,353.84 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M658.430254,343.31015 C662.291622,339.36152 668.622876,339.290779 672.571507,343.152147 C676.448344,346.943308 676.587023,353.115359 672.93623,357.075687 L672.72951,357.2934 L474.782764,559.713519 C470.921396,563.662149 464.590141,563.732889 460.641511,559.871521 C456.764674,556.08036 456.625995,549.90831 460.276787,545.947982 L460.483508,545.730268 L658.430254,343.31015 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
  </g>
)

export const ResetButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M512,322 C616.934102,322 702,407.065898 702,512 C702,616.934102 616.934102,702 512,702 C506.477153,702 502,697.522847 502,692 C502,686.57076 506.326671,682.152068 511.720095,682.003842 L512,682 C605.888407,682 682,605.888407 682,512 C682,418.111593 605.888407,342 512,342 C418.111593,342 342,418.111593 342,512 C342,517.522847 337.522847,522 332,522 C326.477153,522 322,517.522847 322,512 C322,407.065898 407.065898,322 512,322 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M362,450.213203 C367.522847,450.213203 372,454.690356 372,460.213203 C372,465.642443 367.673329,470.061135 362.279905,470.209362 L362,470.213203 L322,470.213203 C316.57076,470.213203 312.152068,474.539874 312.003842,479.933299 L312,480.213203 L312,520.213203 C312,525.736051 307.522847,530.213203 302,530.213203 C296.57076,530.213203 292.152068,525.886533 292.003842,520.493108 L292,520.213203 L292,480.213203 C292,463.810346 305.164172,450.482146 321.503895,450.217223 L322,450.213203 L362,450.213203 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(332.000000, 490.213203) rotate(225.000000) translate(-332.000000, -490.213203) "
    ></path>
  </g>
)

export const UpdateButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M549.519975,311 L372,311 C344.385763,311 322,333.385763 322,361 L322,657 C322,684.614237 344.385763,707 372,707 L652,707 C679.614237,707 702,684.614237 702,657 L702,457.228178 C702,437.706097 693.847671,419.070907 679.510818,405.820764 L597.030793,329.592586 C584.096712,317.638887 567.131943,311 549.519975,311 Z M372,331 L549.519975,331 C562.099952,331 574.217644,335.742062 583.456274,344.280419 L665.936299,420.508596 C676.176908,429.972984 682,443.283834 682,457.228178 L682,657 C682,673.568542 668.568542,687 652,687 L372,687 C355.431458,687 342,673.568542 342,657 L342,361 C342,344.431458 355.431458,331 372,331 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M395,321 L395,371 C395,376.42924 399.326671,380.847932 404.720095,380.996158 L405,381 L502,381 C507.42924,381 511.847932,376.673329 511.996158,371.279905 L512,371 L512,321 L532,321 L532,371 C532,387.402857 518.835828,400.731057 502.496105,400.995981 L502,401 L405,401 C388.597143,401 375.268943,387.835828 375.004019,371.496105 L375,371 L375,321 L395,321 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M397.5,597 L397.5,677 C397.5,682.42924 401.826671,686.847932 407.220095,686.996158 L407.5,687 L616.5,687 C621.92924,687 626.347932,682.673329 626.496158,677.279905 L626.5,677 L626.5,597 L646.5,597 L646.5,677 C646.5,693.402857 633.335828,706.731057 616.996105,706.995981 L616.5,707 L407.5,707 C391.097143,707 377.768943,693.835828 377.504019,677.496105 L377.5,677 L377.5,597 L397.5,597 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(512.000000, 652.000000) scale(1, -1) translate(-512.000000, -652.000000) "
    ></path>
  </g>
)

export const OpenPageButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <g
      transform="translate(438.500000, 421.000000)"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    >
      <path d="M123,33.2132034 C128.522847,33.2132034 133,37.6903559 133,43.2132034 C133,48.6424433 128.673329,53.0611352 123.279905,53.2093616 L123,53.2132034 L40,53.2132034 C23.5971429,53.2132034 10.2689429,66.3773749 10.0040191,82.7170986 L10,83.2132034 L10,181.213203 C10,186.736051 5.5228475,191.213203 0,191.213203 C-5.42923991,191.213203 -9.84793172,186.886533 -9.9961582,181.493108 L-10,181.213203 L-10,83.2132034 C-10,55.8751083 11.9402858,33.6614415 39.1731586,33.219902 L40,33.2132034 L123,33.2132034 Z"></path>
      <path
        d="M133.926407,3.21320344 C139.449254,3.21320344 143.926407,7.69035594 143.926407,13.2132034 C143.926407,18.6424433 139.599736,23.0611352 134.206312,23.2093616 L133.926407,23.2132034 L93.9264069,23.2132034 C88.497167,23.2132034 84.0784752,27.5398742 83.9302487,32.9332986 L83.9264069,33.2132034 L83.9264069,73.2132034 C83.9264069,78.7360509 79.4492544,83.2132034 73.9264069,83.2132034 C68.497167,83.2132034 64.0784752,78.8865326 63.9302487,73.4931083 L63.9264069,73.2132034 L63.9264069,33.2132034 C63.9264069,16.8103464 77.0905784,3.48214629 93.430302,3.21722258 L93.9264069,3.21320344 L133.926407,3.21320344 Z"
        transform="translate(103.926407, 43.213203) rotate(135.000000) translate(-103.926407, -43.213203) "
      ></path>
    </g>
    <path
      d="M512,322 C407.065898,322 322,407.065898 322,512 C322,616.934102 407.065898,702 512,702 C616.934102,702 702,616.934102 702,512 C702,407.065898 616.934102,322 512,322 Z M512,342 C605.888407,342 682,418.111593 682,512 C682,605.888407 605.888407,682 512,682 C418.111593,682 342,605.888407 342,512 C342,418.111593 418.111593,342 512,342 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
  </g>
)

export const CustomButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M459.627934,315.876727 L459.47481,315.882155 C448.426223,315.887387 439.474321,324.850702 439.488988,335.900192 L439.631704,637.238594 C439.633717,641.489223 440.989934,645.628668 443.503602,649.056397 L480.703515,699.783551 C481.650545,701.074956 482.749254,702.247943 483.976035,703.277311 C492.437608,710.377252 505.052708,709.273431 512.152649,700.811858 L553.260074,651.820816 C556.260604,648.244842 557.91576,643.731941 557.938797,639.06394 L559.434776,335.911563 L559.431869,335.56631 C559.248031,324.655616 550.365442,315.886887 539.434806,315.886887 L459.871,315.886 L459.688923,315.876727 C459.66796,315.876727 459.647898,315.876727 459.627934,315.876727 Z M496.831615,687.956278 L459.631702,637.229124 L459.489,335.886 L539.434806,335.886 L537.939041,638.965236 L496.831615,687.956278 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(499.461873, 511.916736) scale(-1, 1) rotate(-45.000000) translate(-499.461873, -511.916736) "
    ></path>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(589.927912, 424.572088) rotate(585.000000) translate(-589.927912, -424.572088) "
      x="579.927912"
      y="374.572088"
      width="20"
      height="100"
      rx="10"
    ></rect>
    <path
      d="M547,319 L467,319 C455.954305,319 447,327.954305 447,339 L447,676 C447,687.045695 455.954305,696 467,696 L547,696 C558.045695,696 567,687.045695 567,676 L567,339 C567,327.954305 558.045695,319 547,319 Z M467,339 L547,339 L547,676 L467,676 L467,339 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(507.000000, 507.500000) scale(-1, 1) rotate(45.000000) translate(-507.000000, -507.500000) "
    ></path>
    <path
      d="M415.528493,412.81529 C421.051341,412.81529 425.528493,417.292442 425.528493,422.81529 L425.528493,462.81529 L425.528493,462.81529 L405.528493,462.81529 L405.528493,422.81529 C405.528493,417.292442 410.005646,412.81529 415.528493,412.81529 Z"
      fill="var(--dn-brand-color)"
      transform="translate(415.528493, 437.815290) scale(-1, 1) rotate(675.000000) translate(-415.528493, -437.815290) "
    ></path>
    <path
      d="M573.920412,571.207209 C579.44326,571.207209 583.920412,575.684361 583.920412,581.207209 L583.920412,621.207209 L583.920412,621.207209 L563.920412,621.207209 L563.920412,581.207209 C563.920412,575.684361 568.397565,571.207209 573.920412,571.207209 Z"
      fill="var(--dn-brand-color)"
      transform="translate(573.920412, 596.207209) scale(-1, 1) rotate(675.000000) translate(-573.920412, -596.207209) "
    ></path>
  </g>
)

export const ConfirmButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M512,322 C407.065898,322 322,407.065898 322,512 C322,616.934102 407.065898,702 512,702 C616.934102,702 702,616.934102 702,512 C702,407.065898 616.934102,322 512,322 Z M512,342 C605.888407,342 682,418.111593 682,512 C682,605.888407 605.888407,682 512,682 C418.111593,682 342,605.888407 342,512 C342,418.111593 418.111593,342 512,342 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <g
      transform="translate(390.500000, 434.000000)"
      fill="var(--dn-brand-color)"
    >
      <rect
        transform="translate(57.500000, 99.071068) scale(-1, 1) rotate(45.000000) translate(-57.500000, -99.071068) "
        x="47.5"
        y="29.0710678"
        width="20"
        height="140"
        rx="10"
      ></rect>
      <rect
        transform="translate(164.426407, 77.857864) rotate(45.000000) translate(-164.426407, -77.857864) "
        x="154.426407"
        y="-22.1421356"
        width="20"
        height="200"
        rx="10"
      ></rect>
    </g>
  </g>
)

export const CancelButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M512,322 C407.065898,322 322,407.065898 322,512 C322,616.934102 407.065898,702 512,702 C616.934102,702 702,616.934102 702,512 C702,407.065898 616.934102,322 512,322 Z M512,342 C605.888407,342 682,418.111593 682,512 C682,605.888407 605.888407,682 512,682 C418.111593,682 342,605.888407 342,512 C342,418.111593 418.111593,342 512,342 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(512.000000, 512.000000) rotate(45.000000) translate(-512.000000, -512.000000) "
      x="502"
      y="332"
      width="20"
      height="360"
      rx="10"
    ></rect>
  </g>
)

export const DetailLinkButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <g
      transform="translate(442.000000, 436.000000)"
      fill="var(--dn-brand-color)"
    >
      <rect
        transform="translate(70.000000, 10.000000) scale(-1, 1) rotate(630.000000) translate(-70.000000, -10.000000) "
        x="60"
        y="-60"
        width="20"
        height="140"
        rx="10"
      ></rect>
      <rect
        transform="translate(70.000000, 76.000000) scale(-1, 1) rotate(630.000000) translate(-70.000000, -76.000000) "
        x="60"
        y="6"
        width="20"
        height="140"
        rx="10"
      ></rect>
      <rect
        transform="translate(70.000000, 142.000000) scale(-1, 1) rotate(630.000000) translate(-70.000000, -142.000000) "
        x="60"
        y="72"
        width="20"
        height="140"
        rx="10"
      ></rect>
    </g>
    <g
      transform="translate(659.882687, 643.916739) rotate(45.000000) translate(-659.882687, -643.916739) translate(619.882687, 535.916739)"
      stroke="var(--dn-brand-color)"
      strokeLinecap="round"
      strokeWidth="20"
    >
      <path d="M42,130 L38,130 C17.0131795,130 0,112.98682 0,92 L0,38 C0,17.0131795 17.0131795,0 38,0 L42,0 C62.9868205,0 80,17.0131795 80,38 C80,46.4486328 80,53.7819626 80,60"></path>
      <path
        d="M42,216 L38,216 C17.0131795,216 0,198.98682 0,178 L0,124 C0,103.01318 17.0131795,86 38,86 L42,86 C62.9868205,86 80,103.01318 80,124 C80,132.448633 80,139.781963 80,146"
        transform="translate(40.000000, 151.000000) scale(-1, -1) translate(-40.000000, -151.000000) "
      ></path>
    </g>
    <path
      d="M512,322 C616.934102,322 702,407.065898 702,512 C702,517.522847 697.522847,522 692,522 C686.57076,522 682.152068,517.673329 682.003842,512.279905 L682,512 C682,418.111593 605.888407,342 512,342 C418.111593,342 342,418.111593 342,512 C342,604.949523 416.596972,680.47599 509.188739,681.977225 L512,682 C517.522847,682 522,686.477153 522,692 C522,697.522847 517.522847,702 512,702 C407.065898,702 322,616.934102 322,512 C322,407.065898 407.065898,322 512,322 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
  </g>
)

export const CreateLinkButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(511.799425, 511.799425) scale(-1, 1) rotate(630.000000) translate(-511.799425, -511.799425) "
      x="501.799425"
      y="411.799425"
      width="20"
      height="200"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(511.799425, 511.799425) scale(-1, 1) translate(-511.799425, -511.799425) "
      x="501.799425"
      y="411.799425"
      width="20"
      height="200"
      rx="10"
    ></rect>
    <g
      transform="translate(659.882687, 643.916739) rotate(45.000000) translate(-659.882687, -643.916739) translate(619.882687, 535.916739)"
      stroke="var(--dn-brand-color)"
      strokeLinecap="round"
      strokeWidth="20"
    >
      <path d="M42,130 L38,130 C17.0131795,130 0,112.98682 0,92 L0,38 C0,17.0131795 17.0131795,0 38,0 L42,0 C62.9868205,0 80,17.0131795 80,38 C80,46.4486328 80,53.7819626 80,60"></path>
      <path
        d="M42,216 L38,216 C17.0131795,216 0,198.98682 0,178 L0,124 C0,103.01318 17.0131795,86 38,86 L42,86 C62.9868205,86 80,103.01318 80,124 C80,132.448633 80,139.781963 80,146"
        transform="translate(40.000000, 151.000000) scale(-1, -1) translate(-40.000000, -151.000000) "
      ></path>
    </g>
    <path
      d="M512,322 C616.934102,322 702,407.065898 702,512 C702,517.522847 697.522847,522 692,522 C686.57076,522 682.152068,517.673329 682.003842,512.279905 L682,512 C682,418.111593 605.888407,342 512,342 C418.111593,342 342,418.111593 342,512 C342,604.949523 416.596972,680.47599 509.188739,681.977225 L512,682 C517.522847,682 522,686.477153 522,692 C522,697.522847 517.522847,702 512,702 C407.065898,702 322,616.934102 322,512 C322,407.065898 407.065898,322 512,322 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
  </g>
)

export const UpdateLinkButtonSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M944,212 C988.18278,212 1024,247.81722 1024,292 L1024,732 C1024,776.18278 988.18278,812 944,812 L80,812 C35.81722,812 0,776.18278 0,732 L0,292 C0,247.81722 35.81722,212 80,212 L944,212 Z M944,232 L80,232 C47.1942859,232 20.5378857,258.328343 20,291.00779 L20,292 L20,732 C20,764.805714 46.328343,791.462114 79.0077903,791.991962 L80,792 L944,792 C976.805714,792 1003.46211,765.671657 1004,732.99221 L1004,732 L1004,292 C1004,259.194286 977.671657,232.537886 944.99221,232.008038 L944,232 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <path
      d="M549.519975,311 C566.900206,311 583.650158,317.465328 596.518152,329.123493 L597.030793,329.592586 L679.510818,405.820764 C693.686582,418.922029 701.816021,437.288008 702,456.570472 L702,457.228178 L702,505 C702,510.522847 697.522847,515 692,515 C686.57076,515 682.152068,510.673329 682.003842,505.279905 L682,505 L682,457.228178 C682,443.485926 676.34447,430.358939 666.378755,420.922505 L665.936299,420.508596 L583.456274,344.280419 C574.369097,335.882035 562.49648,331.156498 550.138279,331.003819 L549.519975,331 L372,331 C355.597143,331 342.268943,344.164172 342.004019,360.503895 L342,361 L342,657 C342,673.402857 355.164172,686.731057 371.503895,686.995981 L372,687 L513,687 C518.522847,687 523,691.477153 523,697 C523,702.42924 518.673329,706.847932 513.279905,707 L513,707 L372,707 C344.661905,707 322.448238,685.059714 322.006699,657.826841 L322,657 L322,361 C322,333.661905 343.940286,311.448238 371.173159,311.006699 L372,311 L549.519975,311 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M395,321 L395,371 C395,376.42924 399.326671,380.847932 404.720095,380.996158 L405,381 L502,381 C507.42924,381 511.847932,376.673329 511.996158,371.279905 L512,371 L512,321 L532,321 L532,371 C532,387.402857 518.835828,400.731057 502.496105,400.995981 L502,401 L405,401 C388.597143,401 375.268943,387.835828 375.004019,371.496105 L375,371 L375,321 L395,321 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
    ></path>
    <path
      d="M397,597 L397,677 C397,682.42924 401.326671,686.847932 406.720095,686.996158 L407,687 L492,687 C497.42924,687 501.847932,682.673329 501.996158,677.279905 L502,677 L502,597 L522,597 L522,677 C522,693.402857 508.835828,706.731057 492.496105,706.995981 L492,707 L407,707 C390.597143,707 377.268943,693.835828 377.004019,677.496105 L377,677 L377,597 L397,597 Z"
      fill="var(--dn-brand-color)"
      fillRule="nonzero"
      transform="translate(449.500000, 652.000000) scale(1, -1) translate(-449.500000, -652.000000) "
    ></path>
    <g
      transform="translate(659.882687, 643.916739) rotate(45.000000) translate(-659.882687, -643.916739) translate(619.882687, 535.916739)"
      stroke="var(--dn-brand-color)"
      strokeLinecap="round"
      strokeWidth="20"
    >
      <path d="M42,130 L38,130 C17.0131795,130 0,112.98682 0,92 L0,38 C0,17.0131795 17.0131795,0 38,0 L42,0 C62.9868205,0 80,17.0131795 80,38 C80,46.4486328 80,53.7819626 80,60"></path>
      <path
        d="M42,216 L38,216 C17.0131795,216 0,198.98682 0,178 L0,124 C0,103.01318 17.0131795,86 38,86 L42,86 C62.9868205,86 80,103.01318 80,124 C80,132.448633 80,139.781963 80,146"
        transform="translate(40.000000, 151.000000) scale(-1, -1) translate(-40.000000, -151.000000) "
      ></path>
    </g>
  </g>
)

export const DataTableSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M880,0 C929.208571,0 969.193171,39.4925145 969.987943,88.5116854 L970,90 L970,607 L950,607 L950,90 C950,51.7266668 919.2836,20.6275333 881.157578,20.009378 L880,20 L90,20 C51.7266668,20 20.6275333,50.7164002 20.009378,88.842422 L20,90 L20,830 C20,868.273333 50.7164002,899.372467 88.842422,899.990622 L90,900 L695,900 L695,920 L90,920 C40.7914288,920 0.806828576,880.507485 0.0120574286,831.488315 L0,830 L0,90 C0,40.7914288 39.4925145,0.806828576 88.5116854,0.0120574286 L90,0 L880,0 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(482.500000, 463.833333) scale(-1, 1) rotate(90.000000) translate(-482.500000, -463.833333) "
      x="472.5"
      y="-7.16666667"
      width="20"
      height="942"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(482.500000, 239.055556) scale(-1, 1) rotate(90.000000) translate(-482.500000, -239.055556) "
      x="472.5"
      y="-231.944444"
      width="20"
      height="942"
    ></rect>
    <g transform="translate(684.639279, 588.000000)">
      <rect
        fill="#999999"
        transform="translate(10.657480, 213.058014) scale(-1, 1) translate(-10.657480, -213.058014) "
        x="0.657479548"
        y="81.8004988"
        width="20"
        height="262.51503"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(329.360721, 213.058014) scale(-1, 1) translate(-329.360721, -213.058014) "
        x="319.360721"
        y="81.8004988"
        width="20"
        height="262.51503"
      ></rect>
      <path
        d="M170.0091,-10 C77.1992035,-10 0.657479548,29.2914183 0.657479548,81.8004988 C0.657479548,134.309579 77.1992035,173.600998 170.0091,173.600998 C262.818997,173.600998 339.360721,134.309579 339.360721,81.8004988 C339.360721,29.2914183 262.818997,-10 170.0091,-10 Z M170.0091,10 C253.214144,10 319.360721,43.9552432 319.360721,81.8004988 C319.360721,119.645754 253.214144,153.600998 170.0091,153.600998 C86.8040573,153.600998 20.6574795,119.645754 20.6574795,81.8004988 C20.6574795,43.9552432 86.8040573,10 170.0091,10 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,169.266833 C20.6574795,207.112088 86.8040573,241.067332 170.0091,241.067332 C252.382093,241.067332 318.036084,207.787798 319.340927,170.400994 L319.360721,169.266833 L339.360721,169.266833 C339.360721,221.775913 262.818997,261.067332 170.0091,261.067332 C78.1273025,261.067332 2.18994084,222.557813 0.680381791,170.838114 L0.657479548,169.266833 L20.6574795,169.266833 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,344.315529 C20.6574795,382.160784 86.8040573,416.116028 170.0091,416.116028 C252.382093,416.116028 318.036084,382.836494 319.340927,345.449689 L319.360721,344.315529 L339.360721,344.315529 C339.360721,396.824609 262.818997,436.116028 170.0091,436.116028 C78.1273025,436.116028 2.18994084,397.606508 0.680381791,345.88681 L0.657479548,344.315529 L20.6574795,344.315529 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,256.733167 C20.6574795,294.578423 86.8040573,328.533666 170.0091,328.533666 C252.382093,328.533666 318.036084,295.254132 319.340927,257.867328 L319.360721,256.733167 L339.360721,256.733167 C339.360721,309.242248 262.818997,348.533666 170.0091,348.533666 C78.1273025,348.533666 2.18994084,310.024147 0.680381791,258.304448 L0.657479548,256.733167 L20.6574795,256.733167 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
    </g>
    <rect
      fill="#999999"
      transform="translate(353.500000, 688.611111) scale(-1, 1) rotate(90.000000) translate(-353.500000, -688.611111) "
      x="343.5"
      y="346.611111"
      width="20"
      height="684"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 351.444444) scale(-1, 1) rotate(630.000000) translate(-189.000000, -351.444444) "
      x="179"
      y="276.444444"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 126.666667) scale(-1, 1) rotate(630.000000) translate(-189.000000, -126.666667) "
      x="179"
      y="51.6666667"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 576.222222) scale(-1, 1) rotate(630.000000) translate(-189.000000, -576.222222) "
      x="179"
      y="501.222222"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 801.000000) scale(-1, 1) rotate(630.000000) translate(-189.000000, -801.000000) "
      x="179"
      y="726"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(486.000000, 351.444444) scale(-1, 1) rotate(630.000000) translate(-486.000000, -351.444444) "
      x="476"
      y="276.444444"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(486.000000, 126.666667) scale(-1, 1) rotate(630.000000) translate(-486.000000, -126.666667) "
      x="476"
      y="51.6666667"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(486.000000, 576.222222) scale(-1, 1) rotate(630.000000) translate(-486.000000, -576.222222) "
      x="476"
      y="501.222222"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(486.000000, 801.000000) scale(-1, 1) rotate(630.000000) translate(-486.000000, -801.000000) "
      x="476"
      y="726"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(783.000000, 351.444444) scale(-1, 1) rotate(630.000000) translate(-783.000000, -351.444444) "
      x="773"
      y="276.444444"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(783.000000, 126.666667) scale(-1, 1) rotate(630.000000) translate(-783.000000, -126.666667) "
      x="773"
      y="51.6666667"
      width="20"
      height="150"
      rx="10"
    ></rect>
  </g>
)

export const DataQueryListSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M880,295 C929.208571,295 969.193171,334.492515 969.987943,383.511685 L970,385 L970,607 L950,607 L950,385 C950,346.726667 919.2836,315.627533 881.157578,315.009378 L880,315 L90,315 C51.7266668,315 20.6275333,345.7164 20.009378,383.842422 L20,385 L20,830 C20,868.273333 50.7164002,899.372467 88.842422,899.990622 L90,900 L695,900 L695,920 L90,920 C40.7914288,920 0.806828576,880.507485 0.0120574286,831.488315 L0,830 L0,385 C0,335.791429 39.4925145,295.806829 88.5116854,295.012057 L90,295 L880,295 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(482.500000, 515.000000) scale(-1, 1) rotate(90.000000) translate(-482.500000, -515.000000) "
      x="472.5"
      y="44"
      width="20"
      height="942"
    ></rect>
    <g transform="translate(684.639279, 588.000000)">
      <rect
        fill="#999999"
        transform="translate(10.657480, 213.058014) scale(-1, 1) translate(-10.657480, -213.058014) "
        x="0.657479548"
        y="81.8004988"
        width="20"
        height="262.51503"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(329.360721, 213.058014) scale(-1, 1) translate(-329.360721, -213.058014) "
        x="319.360721"
        y="81.8004988"
        width="20"
        height="262.51503"
      ></rect>
      <path
        d="M170.0091,-10 C77.1992035,-10 0.657479548,29.2914183 0.657479548,81.8004988 C0.657479548,134.309579 77.1992035,173.600998 170.0091,173.600998 C262.818997,173.600998 339.360721,134.309579 339.360721,81.8004988 C339.360721,29.2914183 262.818997,-10 170.0091,-10 Z M170.0091,10 C253.214144,10 319.360721,43.9552432 319.360721,81.8004988 C319.360721,119.645754 253.214144,153.600998 170.0091,153.600998 C86.8040573,153.600998 20.6574795,119.645754 20.6574795,81.8004988 C20.6574795,43.9552432 86.8040573,10 170.0091,10 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,169.266833 C20.6574795,207.112088 86.8040573,241.067332 170.0091,241.067332 C252.382093,241.067332 318.036084,207.787798 319.340927,170.400994 L319.360721,169.266833 L339.360721,169.266833 C339.360721,221.775913 262.818997,261.067332 170.0091,261.067332 C78.1273025,261.067332 2.18994084,222.557813 0.680381791,170.838114 L0.657479548,169.266833 L20.6574795,169.266833 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,344.315529 C20.6574795,382.160784 86.8040573,416.116028 170.0091,416.116028 C252.382093,416.116028 318.036084,382.836494 319.340927,345.449689 L319.360721,344.315529 L339.360721,344.315529 C339.360721,396.824609 262.818997,436.116028 170.0091,436.116028 C78.1273025,436.116028 2.18994084,397.606508 0.680381791,345.88681 L0.657479548,344.315529 L20.6574795,344.315529 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,256.733167 C20.6574795,294.578423 86.8040573,328.533666 170.0091,328.533666 C252.382093,328.533666 318.036084,295.254132 319.340927,257.867328 L319.360721,256.733167 L339.360721,256.733167 C339.360721,309.242248 262.818997,348.533666 170.0091,348.533666 C78.1273025,348.533666 2.18994084,310.024147 0.680381791,258.304448 L0.657479548,256.733167 L20.6574795,256.733167 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
    </g>
    <rect
      fill="#999999"
      transform="translate(353.500000, 709.000000) scale(-1, 1) rotate(90.000000) translate(-353.500000, -709.000000) "
      x="343.5"
      y="367"
      width="20"
      height="684"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(272.500000, 125.500000) scale(-1, 1) rotate(630.000000) translate(-272.500000, -125.500000) "
      x="262.5"
      y="-33"
      width="20"
      height="317"
      rx="10"
    ></rect>
    <path
      d="M920,0 L50,0 C22.3857625,0 0,22.3857625 0,50 L0,201 C0,228.614237 22.3857625,251 50,251 L920,251 C947.614237,251 970,228.614237 970,201 L970,50 C970,22.3857625 947.614237,0 920,0 Z M50,20 L920,20 C936.568542,20 950,33.4314575 950,50 L950,201 C950,217.568542 936.568542,231 920,231 L50,231 C33.4314575,231 20,217.568542 20,201 L20,50 C20,33.4314575 33.4314575,20 50,20 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <rect
      fill="var(--dn-brand-color)"
      x="521.5"
      y="95.5"
      width="150"
      height="60"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      x="708"
      y="95.5"
      width="150"
      height="60"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 414.666667) scale(-1, 1) rotate(630.000000) translate(-189.000000, -414.666667) "
      x="179"
      y="339.666667"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 607.000000) scale(-1, 1) rotate(630.000000) translate(-189.000000, -607.000000) "
      x="179"
      y="532"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 801.000000) scale(-1, 1) rotate(630.000000) translate(-189.000000, -801.000000) "
      x="179"
      y="726"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(486.000000, 414.666667) scale(-1, 1) rotate(630.000000) translate(-486.000000, -414.666667) "
      x="476"
      y="339.666667"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(486.000000, 607.000000) scale(-1, 1) rotate(630.000000) translate(-486.000000, -607.000000) "
      x="476"
      y="532"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(486.000000, 801.000000) scale(-1, 1) rotate(630.000000) translate(-486.000000, -801.000000) "
      x="476"
      y="726"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(783.000000, 414.666667) scale(-1, 1) rotate(630.000000) translate(-783.000000, -414.666667) "
      x="773"
      y="339.666667"
      width="20"
      height="150"
      rx="10"
    ></rect>
  </g>
)

export const DataFormSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M90,920 C40.7914288,920 0.806828576,880.507485 0.0120574286,831.488315 L0,830 L0,90 C0,40.7914288 39.4925145,0.806828576 88.5116854,0.0120574286 L90,0 L880,0 C929.208571,0 969.193171,39.4925145 969.987943,88.5116854 L970,90 L970,610 L950,610 L950,90 C950,51.7266668 919.2836,20.6275333 881.157578,20.009378 L880,20 L90,20 C51.7266668,20 20.6275333,50.7164002 20.009378,88.842422 L20,90 L20,830 C20,868.273333 50.7164002,899.372467 88.842422,899.990622 L90,900 L695,900 L695,920 L90,920 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(479.500000, 223.700000) scale(-1, 1) rotate(90.000000) translate(-479.500000, -223.700000) "
      x="469.5"
      y="-142.3"
      width="20"
      height="732"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(479.500000, 443.100000) scale(-1, 1) rotate(90.000000) translate(-479.500000, -443.100000) "
      x="469.5"
      y="77.1"
      width="20"
      height="732"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(313.000000, 662.500000) scale(-1, 1) rotate(90.000000) translate(-313.000000, -662.500000) "
      x="303"
      y="463"
      width="20"
      height="399"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 114.000000) scale(-1, 1) rotate(630.000000) translate(-189.000000, -114.000000) "
      x="179"
      y="39"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 333.400000) scale(-1, 1) rotate(630.000000) translate(-189.000000, -333.400000) "
      x="179"
      y="258.4"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 552.800000) scale(-1, 1) rotate(630.000000) translate(-189.000000, -552.800000) "
      x="179"
      y="477.8"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      x="113.5"
      y="754"
      width="150"
      height="60"
      rx="10"
    ></rect>
    <rect fill="#999999" x="300" y="754" width="150" height="60" rx="10"></rect>
    <g transform="translate(684.639279, 588.000000)">
      <rect
        fill="#999999"
        transform="translate(10.657480, 213.058014) scale(-1, 1) translate(-10.657480, -213.058014) "
        x="0.657479548"
        y="81.8004988"
        width="20"
        height="262.51503"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(329.360721, 213.058014) scale(-1, 1) translate(-329.360721, -213.058014) "
        x="319.360721"
        y="81.8004988"
        width="20"
        height="262.51503"
      ></rect>
      <path
        d="M170.0091,-10 C77.1992035,-10 0.657479548,29.2914183 0.657479548,81.8004988 C0.657479548,134.309579 77.1992035,173.600998 170.0091,173.600998 C262.818997,173.600998 339.360721,134.309579 339.360721,81.8004988 C339.360721,29.2914183 262.818997,-10 170.0091,-10 Z M170.0091,10 C253.214144,10 319.360721,43.9552432 319.360721,81.8004988 C319.360721,119.645754 253.214144,153.600998 170.0091,153.600998 C86.8040573,153.600998 20.6574795,119.645754 20.6574795,81.8004988 C20.6574795,43.9552432 86.8040573,10 170.0091,10 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,169.266833 C20.6574795,207.112088 86.8040573,241.067332 170.0091,241.067332 C252.382093,241.067332 318.036084,207.787798 319.340927,170.400994 L319.360721,169.266833 L339.360721,169.266833 C339.360721,221.775913 262.818997,261.067332 170.0091,261.067332 C78.1273025,261.067332 2.18994084,222.557813 0.680381791,170.838114 L0.657479548,169.266833 L20.6574795,169.266833 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,344.315529 C20.6574795,382.160784 86.8040573,416.116028 170.0091,416.116028 C252.382093,416.116028 318.036084,382.836494 319.340927,345.449689 L319.360721,344.315529 L339.360721,344.315529 C339.360721,396.824609 262.818997,436.116028 170.0091,436.116028 C78.1273025,436.116028 2.18994084,397.606508 0.680381791,345.88681 L0.657479548,344.315529 L20.6574795,344.315529 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,256.733167 C20.6574795,294.578423 86.8040573,328.533666 170.0091,328.533666 C252.382093,328.533666 318.036084,295.254132 319.340927,257.867328 L319.360721,256.733167 L339.360721,256.733167 C339.360721,309.242248 262.818997,348.533666 170.0091,348.533666 C78.1273025,348.533666 2.18994084,310.024147 0.680381791,258.304448 L0.657479548,256.733167 L20.6574795,256.733167 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
    </g>
  </g>
)

export const DataDetailFormSource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <path
      d="M90,920 C40.7914288,920 0.806828576,880.507485 0.0120574286,831.488315 L0,830 L0,90 C0,40.7914288 39.4925145,0.806828576 88.5116854,0.0120574286 L90,0 L880,0 C929.208571,0 969.193171,39.4925145 969.987943,88.5116854 L970,90 L970,610 L950,610 L950,90 C950,51.7266668 919.2836,20.6275333 881.157578,20.009378 L880,20 L90,20 C51.7266668,20 20.6275333,50.7164002 20.009378,88.842422 L20,90 L20,830 C20,868.273333 50.7164002,899.372467 88.842422,899.990622 L90,900 L695,900 L695,920 L90,920 Z"
      fill="#999999"
      fillRule="nonzero"
    ></path>
    <rect
      fill="#999999"
      transform="translate(479.500000, 223.700000) scale(-1, 1) rotate(90.000000) translate(-479.500000, -223.700000) "
      x="469.5"
      y="-142.3"
      width="20"
      height="732"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(479.500000, 507.600000) scale(-1, 1) rotate(90.000000) translate(-479.500000, -507.600000) "
      x="469.5"
      y="141.6"
      width="20"
      height="732"
      rx="10"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(313.000000, 791.500000) scale(-1, 1) rotate(90.000000) translate(-313.000000, -791.500000) "
      x="303"
      y="592"
      width="20"
      height="399"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 114.000000) scale(-1, 1) rotate(630.000000) translate(-189.000000, -114.000000) "
      x="179"
      y="39"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 365.650000) scale(-1, 1) rotate(630.000000) translate(-189.000000, -365.650000) "
      x="179"
      y="290.65"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(189.000000, 649.550000) scale(-1, 1) rotate(630.000000) translate(-189.000000, -649.550000) "
      x="179"
      y="574.55"
      width="20"
      height="150"
      rx="10"
    ></rect>
    <g transform="translate(684.639279, 588.000000)">
      <rect
        fill="#999999"
        transform="translate(10.657480, 213.058014) scale(-1, 1) translate(-10.657480, -213.058014) "
        x="0.657479548"
        y="81.8004988"
        width="20"
        height="262.51503"
      ></rect>
      <rect
        fill="#999999"
        transform="translate(329.360721, 213.058014) scale(-1, 1) translate(-329.360721, -213.058014) "
        x="319.360721"
        y="81.8004988"
        width="20"
        height="262.51503"
      ></rect>
      <path
        d="M170.0091,-10 C77.1992035,-10 0.657479548,29.2914183 0.657479548,81.8004988 C0.657479548,134.309579 77.1992035,173.600998 170.0091,173.600998 C262.818997,173.600998 339.360721,134.309579 339.360721,81.8004988 C339.360721,29.2914183 262.818997,-10 170.0091,-10 Z M170.0091,10 C253.214144,10 319.360721,43.9552432 319.360721,81.8004988 C319.360721,119.645754 253.214144,153.600998 170.0091,153.600998 C86.8040573,153.600998 20.6574795,119.645754 20.6574795,81.8004988 C20.6574795,43.9552432 86.8040573,10 170.0091,10 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,169.266833 C20.6574795,207.112088 86.8040573,241.067332 170.0091,241.067332 C252.382093,241.067332 318.036084,207.787798 319.340927,170.400994 L319.360721,169.266833 L339.360721,169.266833 C339.360721,221.775913 262.818997,261.067332 170.0091,261.067332 C78.1273025,261.067332 2.18994084,222.557813 0.680381791,170.838114 L0.657479548,169.266833 L20.6574795,169.266833 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,344.315529 C20.6574795,382.160784 86.8040573,416.116028 170.0091,416.116028 C252.382093,416.116028 318.036084,382.836494 319.340927,345.449689 L319.360721,344.315529 L339.360721,344.315529 C339.360721,396.824609 262.818997,436.116028 170.0091,436.116028 C78.1273025,436.116028 2.18994084,397.606508 0.680381791,345.88681 L0.657479548,344.315529 L20.6574795,344.315529 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
      <path
        d="M20.6574795,256.733167 C20.6574795,294.578423 86.8040573,328.533666 170.0091,328.533666 C252.382093,328.533666 318.036084,295.254132 319.340927,257.867328 L319.360721,256.733167 L339.360721,256.733167 C339.360721,309.242248 262.818997,348.533666 170.0091,348.533666 C78.1273025,348.533666 2.18994084,310.024147 0.680381791,258.304448 L0.657479548,256.733167 L20.6574795,256.733167 Z"
        fill="#979797"
        fillRule="nonzero"
      ></path>
    </g>
  </g>
)

export const EntitySource = (
  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
    <rect
      stroke="#999999"
      strokeWidth="20"
      x="10"
      y="10"
      width="1004"
      height="1004"
      rx="80"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(513.500000, 210.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -210.000000) "
      x="503.5"
      y="-290.5"
      width="20"
      height="1001"
      rx="10"
    ></rect>
    <path
      d="M517.661479,308.887976 L273.916036,449.614473 C267.728014,453.187129 263.916036,459.78967 263.916036,466.934982 L263.916036,748.387976 C263.916036,755.533288 267.728014,762.135828 273.916036,765.708484 L517.661479,906.434982 C523.8495,910.007637 531.473457,910.007637 537.661479,906.434982 L781.406922,765.708484 C787.594944,762.135828 791.406922,755.533288 791.406922,748.387976 L791.406922,466.934982 C791.406922,459.78967 787.594944,453.187129 781.406922,449.614473 L537.661479,308.887976 C531.473457,305.31532 523.8495,305.31532 517.661479,308.887976 Z M283.916036,466.934982 L527.661479,326.208484 L771.406922,466.934982 L771.406922,748.387976 L527.661479,889.114473 L283.916036,748.387976 L283.916036,466.934982 Z"
      fill="#979797"
      fillRule="nonzero"
    ></path>
    <g
      transform="translate(457.661479, 470.000000)"
      fill="var(--dn-brand-color)"
    >
      <rect
        transform="translate(70.000000, 10.000000) scale(-1, 1) rotate(630.000000) translate(-70.000000, -10.000000) "
        x="60"
        y="-60"
        width="20"
        height="140"
        rx="10"
      ></rect>
      <rect
        transform="translate(50.000000, 130.500000) scale(-1, 1) rotate(630.000000) translate(-50.000000, -130.500000) "
        x="40"
        y="90.5"
        width="20"
        height="80"
        rx="10"
      ></rect>
      <rect
        transform="translate(82.000000, 130.500000) scale(-1, 1) translate(-82.000000, -130.500000) "
        x="72"
        y="110.5"
        width="20"
        height="40"
        rx="10"
      ></rect>
      <rect
        transform="translate(130.000000, 30.000000) scale(-1, 1) translate(-130.000000, -30.000000) "
        x="120"
        y="0"
        width="20"
        height="60"
        rx="10"
      ></rect>
      <rect
        transform="translate(130.000000, 231.000000) scale(-1, 1) translate(-130.000000, -231.000000) "
        x="120"
        y="201"
        width="20"
        height="60"
        rx="10"
      ></rect>
      <rect
        transform="translate(70.000000, 251.000000) scale(-1, 1) rotate(630.000000) translate(-70.000000, -251.000000) "
        x="60"
        y="181"
        width="20"
        height="140"
        rx="10"
      ></rect>
      <rect
        transform="translate(10.000000, 130.500000) scale(-1, 1) translate(-10.000000, -130.500000) "
        x="0"
        y="0"
        width="20"
        height="261"
        rx="10"
      ></rect>
    </g>
  </g>
)

export const ImageEntitySource = (
  <g stroke="none" fill="none">
    <path
      d="M944,0 C988.18278,0 1024,35.81722 1024,80 L1024,944 C1024,988.18278 988.18278,1024 944,1024 L80,1024 C35.81722,1024 0,988.18278 0,944 L0,80 C0,35.81722 35.81722,0 80,0 L944,0 Z M944,20 L80,20 C47.1942859,20 20.5378857,46.328343 20,79.0077903 L20,80 L20,944 C20,976.805714 46.328343,1003.46211 79.0077903,1004 L80,1004 L944,1004 C976.805714,1004 1003.46211,977.671657 1004,944.99221 L1004,944 L1004,80 C1004,47.1942859 977.671657,20.5378857 944.99221,20 L944,20 Z"
      fill="#999999"
    ></path>
    <rect
      fill="#999999"
      transform="translate(513.500000, 210.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -210.000000) "
      x="503.5"
      y="-290.5"
      width="20"
      height="1001"
      rx="10"
    ></rect>
    <path
      d="M501.745443,308.887976 L258,449.614473 C251.811978,453.187129 248,459.78967 248,466.934982 L248,748.387976 C248,755.533288 251.811978,762.135828 258,765.708484 L501.745443,906.434982 C507.933465,910.007637 515.557422,910.007637 521.745443,906.434982 L765.490887,765.708484 C771.678908,762.135828 775.490887,755.533288 775.490887,748.387976 L775.490887,466.934982 C775.490887,459.78967 771.678908,453.187129 765.490887,449.614473 L521.745443,308.887976 C515.557422,305.31532 507.933465,305.31532 501.745443,308.887976 Z M268,466.934982 L511.745443,326.208484 L755.490887,466.934982 L755.490887,748.387976 L511.745443,889.114473 L268,748.387976 L268,466.934982 Z"
      fill="#979797"
    ></path>
    <path
      d="M444.694491,438 C484.071424,438 515.992736,469.921312 515.992736,509.298246 C515.992736,548.675179 484.071424,580.596491 444.694491,580.596491 C405.317557,580.596491 373.396245,548.675179 373.396245,509.298246 C373.396245,469.921312 405.317557,438 444.694491,438 Z M444.694491,458 C416.363252,458 393.396245,480.967007 393.396245,509.298246 C393.396245,537.629484 416.363252,560.596491 444.694491,560.596491 C473.025729,560.596491 495.992736,537.629484 495.992736,509.298246 C495.992736,480.967007 473.025729,458 444.694491,458 Z"
      fill="var(--dn-brand-color)"
    ></path>
    <path
      d="M622.492294,598.549112 C638.021928,582.837333 663.348097,582.689676 679.059875,598.21931 C680.206394,599.352537 681.283184,600.553865 682.28442,601.816661 L682.778715,602.45315 L773.63287,721.908356 L757.714006,734.015782 L666.85985,614.560576 C666.290581,613.8121 665.669233,613.104705 665.00043,612.443656 C657.234839,604.768089 644.771927,604.752006 636.986319,612.340748 L636.71664,612.608556 L532.459335,718.088677 C519.538645,731.160912 499.394504,733.730454 483.623258,724.384289 L483.146692,724.097423 L416.986275,683.650209 C408.549633,678.492463 397.630586,680.28107 391.275853,687.807782 L391.033706,688.100098 L312.844261,784.306964 L297.323668,771.693036 L375.513113,675.48617 C388.019775,660.097561 409.885948,656.215893 426.904049,666.277232 L427.418251,666.586391 L493.578668,707.033605 C501.383946,711.805367 511.413908,710.666156 517.948964,704.312903 L518.23499,704.029232 L622.492294,598.549112 Z"
      fill="var(--dn-brand-color)"
    ></path>
  </g>
)

export const FileEntitySource = (
  <g fill="none">
    <rect
      stroke="#999999"
      strokeWidth="20"
      x="10"
      y="10"
      width="1004"
      height="1004"
      rx="80"
    ></rect>
    <rect
      fill="#999999"
      transform="translate(513.500000, 210.000000) scale(-1, 1) rotate(630.000000) translate(-513.500000, -210.000000) "
      x="503.5"
      y="-290.5"
      width="20"
      height="1001"
      rx="10"
    ></rect>
    <path
      d="M502,308.887976 L258.254557,449.614473 C252.066535,453.187129 248.254557,459.78967 248.254557,466.934982 L248.254557,748.387976 C248.254557,755.533288 252.066535,762.135828 258.254557,765.708484 L502,906.434982 C508.188022,910.007637 515.811978,910.007637 522,906.434982 L765.745443,765.708484 C771.933465,762.135828 775.745443,755.533288 775.745443,748.387976 L775.745443,466.934982 C775.745443,459.78967 771.933465,453.187129 765.745443,449.614473 L522,308.887976 C515.811978,305.31532 508.188022,305.31532 502,308.887976 Z M268.254557,466.934982 L512,326.208484 L755.745443,466.934982 L755.745443,748.387976 L512,889.114473 L268.254557,748.387976 L268.254557,466.934982 Z"
      fill="#979797"
    ></path>
    <path
      d="M476.010023,471.661479 C482.948688,471.661479 489.555133,474.537023 494.277456,479.566204 L494.507,479.815 L528.279384,507.830929 L528.855274,508.51466 C529.760103,509.58893 531.06784,510.23569 532.462732,510.308062 L532.725219,510.314865 L590.812749,510.314865 C604.514472,510.314865 615.647855,521.311232 615.869111,534.931862 L615.872467,535.345403 L615.999957,578.970776 L596.000043,579.029224 L595.87251,535.374626 C595.87251,532.653735 593.724837,530.43446 591.032231,530.31954 L590.812749,530.314865 L532.725219,530.314865 C525.786554,530.314865 519.180109,527.439321 514.457786,522.41014 L514.223,522.156 L480.455858,494.145415 L479.879968,493.461684 C478.975139,492.387415 477.667402,491.740654 476.27251,491.668282 L476.010023,491.661479 L396.039841,491.661479 C396.025172,491.661479 396.012354,491.669407 396.005439,491.681211 L396,491.70132 L396,714 C396,719.42924 400.326671,723.847932 405.720095,723.996158 L406,724 L484,724 L484,744 L406,744 C389.597143,744 376.268943,730.835828 376.004019,714.496105 L376,714 L376,491.70132 C376,480.74891 384.786196,471.848626 395.694665,471.664392 L396.039841,471.661479 L476.010023,471.661479 Z"
      fill="var(--dn-brand-color)"
    ></path>
    <path
      d="M617.749004,569 L450.61788,569 C434.049337,569 420.61788,582.431458 420.61788,599 L420.61788,714 C420.61788,730.568542 434.049337,744 450.61788,744 L617.749004,744 C634.317546,744 647.749004,730.568542 647.749004,714 L647.749004,599 C647.749004,582.431458 634.317546,569 617.749004,569 Z M450.61788,589 L617.749004,589 C623.271851,589 627.749004,593.477153 627.749004,599 L627.749004,714 C627.749004,719.522847 623.271851,724 617.749004,724 L450.61788,724 C445.095032,724 440.61788,719.522847 440.61788,714 L440.61788,599 C440.61788,593.477153 445.095032,589 450.61788,589 Z"
      fill="var(--dn-brand-color)"
      transform="translate(534.183442, 656.500000) scale(1, -1) translate(-534.183442, -656.500000) "
    ></path>
  </g>
)

export const FileSource = (
  <g stroke="none" fill="none">
    <rect
      fill="var(--dn-brand-color)"
      transform="translate(612.000000, 628.500000) scale(-1, -1) rotate(900.000000) translate(-612.000000, -628.500000) "
      x="602"
      y="478.5"
      width="20"
      height="300"
      rx="10"
    ></rect>
    <path
      d="M681.5,600.712157 C687.022847,600.712157 691.5,605.18931 691.5,610.712157 C691.5,616.141397 687.173329,620.560089 681.779905,620.708316 L681.5,620.712157 L562.5,620.712157 C557.07076,620.712157 552.652068,625.038828 552.503842,630.432253 L552.5,630.712157 L552.5,749.712157 C552.5,755.235005 548.022847,759.712157 542.5,759.712157 C537.07076,759.712157 532.652068,755.385487 532.503842,749.992062 L532.5,749.712157 L532.5,630.712157 C532.5,614.3093 545.664172,600.9811 562.003895,600.716177 L562.5,600.712157 L681.5,600.712157 Z"
      fill="var(--dn-brand-color)"
      transform="translate(612.000000, 680.212157) scale(1, -1) rotate(45.000000) translate(-612.000000, -680.212157) "
    ></path>
    <path
      d="M368.611363,0 C388.827208,0 408.033481,8.73837915 421.311263,23.926975 L421.547,24.198 L555.753643,135.526842 L556.329533,136.210573 C565.703127,147.33948 579.452383,153.825549 593.98243,153.996532 L594.571904,154 L826,154 C864.273333,154 895.372467,184.7164 895.990622,222.842422 L896,224 L896,351 L876,351 L876,224 C876,196.661905 854.059714,174.448238 826.826841,174.006699 L826,174 L594.571904,174 C574.356059,174 555.149786,165.261621 541.872003,150.073025 L541.639,149.804 L407.429624,38.4731584 L406.853734,37.7894269 C397.48014,26.6605198 383.730884,20.1744513 369.200837,20.0034679 L368.611363,20 L50,20 C33.5971429,20 20.2689429,33.1641715 20.0040191,49.5038951 L20,50 L20,974 C20,990.402857 33.1641715,1003.73106 49.5038951,1003.99598 L50,1004 L50,1024 C22.6619049,1024 0.448238098,1002.05971 0,974.826841 L0,974 L0,50 C0,22.6619049 21.9402858,0.448238098 49.1731586,0 L50,0 L368.611363,0 Z"
      fill="#999999"
    ></path>
    <path
      d="M953,333 L127.763298,333 L127.763298,353 C155.377535,353 177.763298,375.385763 177.763298,403 L177.763298,954 C177.763298,992.659932 209.103365,1024 247.763298,1024 L953,1024 C991.659932,1024 1023,992.659932 1023,954 L1023,403 C1023,364.340068 991.659932,333 953,333 Z M177.260773,353.502525 L176.75,353 L953,353 C980.614237,353 1003,375.385763 1003,403 L1003,954 C1003,981.614237 980.614237,1004 953,1004 L247.763298,1004 C220.14906,1004 197.763298,981.614237 197.763298,954 L197.763298,403 C197.763298,383.670034 189.928281,366.170034 177.260773,353.502525 Z"
      fill="#999999"
      transform="translate(575.381649, 678.500000) scale(1, -1) translate(-575.381649, -678.500000) "
    ></path>
    <path
      d="M769.5,768.5 C774.92924,768.5 779.347932,772.826671 779.496158,778.220095 L779.5,778.5 L779.5,818.5 C779.5,834.902857 766.335828,848.231057 749.996105,848.495981 L749.5,848.5 L474.5,848.5 C458.097143,848.5 444.768943,835.335828 444.504019,818.996105 L444.5,818.5 L444.5,787.5 C444.5,781.977153 448.977153,777.5 454.5,777.5 C459.92924,777.5 464.347932,781.826671 464.496158,787.220095 L464.5,787.5 L464.5,818.5 C464.5,823.92924 468.826671,828.347932 474.220095,828.496158 L474.5,828.5 L749.5,828.5 C754.92924,828.5 759.347932,824.173329 759.496158,818.779905 L759.5,818.5 L759.5,778.5 C759.5,772.977153 763.977153,768.5 769.5,768.5 Z"
      fill="var(--dn-brand-color)"
    ></path>
    <rect
      fill="#999999"
      transform="translate(108.500000, 1014.000000) scale(-1, 1) rotate(90.000000) translate(-108.500000, -1014.000000) "
      x="98.5"
      y="940.5"
      width="20"
      height="147"
      rx="10"
    ></rect>
  </g>
)
