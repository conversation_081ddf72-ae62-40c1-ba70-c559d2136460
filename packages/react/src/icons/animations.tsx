export const DragLeftSourceAnimation = {
  light: {
    shadow: `
      <style>
@keyframes helper-drag-rec_w { 0% { width: 210px; } 54.5455% { width: 210px; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { width: 540px; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { width: 520px; } 100% { width: 520px; } }
@keyframes helper-drag-rec_h { 0% { height: 100px; } 54.5455% { height: 100px; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { height: 560px; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { height: 540px; } 100% { height: 540px; } }
@keyframes helper-drag-rec_mo { 0% { offset-distance: 0%; } 33.3333% { offset-distance: 0.12%; animation-timing-function: cubic-bezier(0.62,0.079,0.616,1); } 54.5455% { offset-distance: 64.297%; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { offset-distance: 98.331%; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
@keyframes helper-drag-mouse_mo { 0% { offset-distance: 0%; animation-timing-function: cubic-bezier(0.69,0.145,0.37,1); } 18.1818% { offset-distance: 37.49%; } 24.2424% { offset-distance: 38.651%; } 33.3333% { offset-distance: 38.651%; animation-timing-function: cubic-bezier(0.62,0.079,0.616,1); } 54.5455% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
@keyframes helper-drag-boomrightup_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a0_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a1_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a2_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes helper-drag-boomleftup_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a3_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a4_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a5_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes helper-drag-boomrightbottom_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a6_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a7_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a8_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes helper-drag-boomleftbottom_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a9_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a10_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a11_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
    </style>
    <rect id="helper-drag-leftrec" width="260" height="600" fill="#eeeeee" stroke="none" fill-rule="evenodd" rx="12" transform="translate(97.4763,336) translate(-27.4763,-124)"/>
    <rect id="helper-drag-rightrec" width="580" height="600" fill="#eeeeee" stroke="none" fill-rule="evenodd" rx="12" transform="translate(443.476,336) translate(-69.5,-124)"/>
    <rect id="helper-drag-rec" width="210" height="100" fill="#ffffff" rx="10" stroke="none" style="transform: translate(-111.034px,-48.1565px); animation: 3.3s linear infinite both helper-drag-rec_w, 3.3s linear infinite both helper-drag-rec_h, 3.3s linear infinite both helper-drag-rec_mo; offset-path: path('M206.034,298.157L205.034,298.157C481.262,280.399,662.717,480.353,670.01,510.157L505.01,280.552L515.01,290.157L515.01,290.157'); offset-rotate: 0deg;"/>
    <g id="helper-drag-mouse" fill="#000000" fill-rule="nonzero" opacity="1" style="transform: scale(2,2) translate(-14px,-14px); animation: 3.3s linear infinite both helper-drag-mouse_mo; offset-path: path('M200,613L200,290L200,300L200,300C371.375,309.667,501.439,327.173,663.976,512L663.976,512'); offset-rotate: 0deg;">
        <path id="helper-drag-mouse-path" d="M27.9059,13.8023L23.4402,10.278C23.364,10.218,23.2602,10.2069,23.173,10.2494C23.0858,10.2919,23.0305,10.3805,23.0308,10.4775L23.0308,12.7418L15.2614,12.7418L15.2614,4.9723L17.5292,4.9723C17.7392,4.9723,17.8582,4.72732,17.7287,4.56283L14.201,0.0971035C14.1537,0.0358608,14.0806,0,14.0032,0C13.9258,0,13.8528,0.0358608,13.8055,0.0971035L10.2778,4.56283C10.2178,4.63907,10.2067,4.74288,10.2492,4.83009C10.2917,4.91729,10.3803,4.97254,10.4773,4.9723L12.7416,4.9723L12.7416,12.7418L4.97221,12.7418L4.97221,10.474C4.97221,10.264,4.72723,10.145,4.56274,10.2745L0.0971017,13.8023C0.0358601,13.8496,0,13.9226,0,14C0,14.0774,0.0358601,14.1504,0.0971017,14.1977L4.55924,17.7255C4.72373,17.855,4.96871,17.7395,4.96871,17.526L4.96871,15.2617L12.7381,15.2617L12.7381,23.0312L10.4703,23.0312C10.2603,23.0312,10.1413,23.2762,10.2708,23.4407L13.7985,27.9029C13.9,28.0324,14.096,28.0324,14.194,27.9029L17.7217,23.4407C17.8512,23.2762,17.7357,23.0312,17.5222,23.0312L15.2614,23.0312L15.2614,15.2617L23.0308,15.2617L23.0308,17.5295C23.0308,17.7395,23.2757,17.8585,23.4402,17.729L27.9024,14.2012C27.9634,14.1533,27.9993,14.0802,28,14.0026C28.0007,13.925,27.966,13.8513,27.9059,13.8023L27.9059,13.8023Z" transform="translate(14,14) translate(-14,-14)"/>
    </g>
    <g id="helper-drag-boomrightup" opacity="0" transform="translate(985.577,207.1) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-drag-boomrightup_o;">
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a0_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a1_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a2_d;"/>
    </g>
    <g id="helper-drag-boomleftup" opacity="0" transform="translate(367.253,188.323) rotate(-90) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-drag-boomleftup_o;">
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a3_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a4_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a5_d;"/>
    </g>
    <g id="helper-drag-boomrightbottom" opacity="0" transform="translate(960.3,845.127) rotate(90) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-drag-boomrightbottom_o;">
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a6_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a7_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a8_d;"/>
    </g>
    <g id="helper-drag-boomleftbottom" opacity="0" transform="translate(335.477,819.85) rotate(-180) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-drag-boomleftbottom_o;">
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a9_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a10_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a11_d;"/>
    </g>
`,
  },
  dark: {
    shadow: `
        <style>
        @keyframes helper-drag-rec_w { 0% { width: 210px; } 54.5455% { width: 210px; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { width: 540px; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { width: 520px; } 100% { width: 520px; } }
        @keyframes helper-drag-rec_h { 0% { height: 100px; } 54.5455% { height: 100px; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { height: 560px; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { height: 540px; } 100% { height: 540px; } }
        @keyframes helper-drag-rec_mo { 0% { offset-distance: 0%; } 33.3333% { offset-distance: 0.12%; animation-timing-function: cubic-bezier(0.62,0.079,0.616,1); } 54.5455% { offset-distance: 64.297%; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { offset-distance: 98.331%; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
        @keyframes helper-drag-mouse_mo { 0% { offset-distance: 0%; animation-timing-function: cubic-bezier(0.69,0.145,0.37,1); } 18.1818% { offset-distance: 37.49%; } 24.2424% { offset-distance: 38.651%; } 33.3333% { offset-distance: 38.651%; animation-timing-function: cubic-bezier(0.62,0.079,0.616,1); } 54.5455% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
        @keyframes helper-drag-boomrightup_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a0_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a1_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a2_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helper-drag-boomleftup_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a3_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a4_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a5_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helper-drag-boomrightbottom_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a6_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a7_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a8_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helper-drag-boomleftbottom_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a9_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a10_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a11_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
            </style>
            <rect id="helper-drag-leftrec" width="260" height="600" fill="#3b3b3b" stroke="none" fill-rule="evenodd" rx="12" transform="translate(97.4763,336) translate(-27.4763,-124)"/>
            <rect id="helper-drag-rightrec" width="580" height="600" fill="#3b3b3b" stroke="none" fill-rule="evenodd" rx="12" transform="translate(443.476,336) translate(-69.5,-124)"/>
            <rect id="helper-drag-rec" width="210" height="100" fill="#1a1a1a" rx="10" stroke="none" style="transform: translate(-111.034px,-48.1565px); animation: 3.3s linear infinite both helper-drag-rec_w, 3.3s linear infinite both helper-drag-rec_h, 3.3s linear infinite both helper-drag-rec_mo; offset-path: path('M206.034,298.157L205.034,298.157C481.262,280.399,662.717,480.353,670.01,510.157L505.01,280.552L515.01,290.157L515.01,290.157'); offset-rotate: 0deg;"/>
            <g id="helper-drag-mouse" fill="#000000" fill-rule="nonzero" opacity="1" style="transform: scale(2,2) translate(-14px,-14px); animation: 3.3s linear infinite both helper-drag-mouse_mo; offset-path: path('M200,613L200,290L200,300L200,300C371.375,309.667,501.439,327.173,663.976,512L663.976,512'); offset-rotate: 0deg;">
                <path id="helper-drag-mouse-path" d="M27.9059,13.8023L23.4402,10.278C23.364,10.218,23.2602,10.2069,23.173,10.2494C23.0858,10.2919,23.0305,10.3805,23.0308,10.4775L23.0308,12.7418L15.2614,12.7418L15.2614,4.9723L17.5292,4.9723C17.7392,4.9723,17.8582,4.72732,17.7287,4.56283L14.201,0.0971035C14.1537,0.0358608,14.0806,0,14.0032,0C13.9258,0,13.8528,0.0358608,13.8055,0.0971035L10.2778,4.56283C10.2178,4.63907,10.2067,4.74288,10.2492,4.83009C10.2917,4.91729,10.3803,4.97254,10.4773,4.9723L12.7416,4.9723L12.7416,12.7418L4.97221,12.7418L4.97221,10.474C4.97221,10.264,4.72723,10.145,4.56274,10.2745L0.0971017,13.8023C0.0358601,13.8496,0,13.9226,0,14C0,14.0774,0.0358601,14.1504,0.0971017,14.1977L4.55924,17.7255C4.72373,17.855,4.96871,17.7395,4.96871,17.526L4.96871,15.2617L12.7381,15.2617L12.7381,23.0312L10.4703,23.0312C10.2603,23.0312,10.1413,23.2762,10.2708,23.4407L13.7985,27.9029C13.9,28.0324,14.096,28.0324,14.194,27.9029L17.7217,23.4407C17.8512,23.2762,17.7357,23.0312,17.5222,23.0312L15.2614,23.0312L15.2614,15.2617L23.0308,15.2617L23.0308,17.5295C23.0308,17.7395,23.2757,17.8585,23.4402,17.729L27.9024,14.2012C27.9634,14.1533,27.9993,14.0802,28,14.0026C28.0007,13.925,27.966,13.8513,27.9059,13.8023L27.9059,13.8023Z" fill="#ffffff" transform="translate(14,14) translate(-14,-14)"/>
            </g>
            <g id="helper-drag-boomrightup" opacity="0" transform="translate(985.577,207.1) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-drag-boomrightup_o;">
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a0_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a1_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a2_d;"/>
            </g>
            <g id="helper-drag-boomleftup" opacity="0" transform="translate(367.253,188.323) rotate(-90) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-drag-boomleftup_o;">
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a3_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a4_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a5_d;"/>
            </g>
            <g id="helper-drag-boomrightbottom" opacity="0" transform="translate(960.3,845.127) rotate(90) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-drag-boomrightbottom_o;">
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a6_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a7_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a8_d;"/>
            </g>
            <g id="helper-drag-boomleftbottom" opacity="0" transform="translate(335.477,819.85) rotate(-180) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-drag-boomleftbottom_o;">
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a9_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a10_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a11_d;"/>
            </g>
        `,
  },
}

export const DragRightSourceAnimation = {
  light: {
    shadow: `
<style>
@keyframes helper-rec_w { 0% { width: 210px; } 54.5455% { width: 210px; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { width: 540px; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { width: 520px; } 100% { width: 520px; } }
@keyframes helper-rec_h { 0% { height: 100px; } 54.5455% { height: 100px; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { height: 560px; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { height: 540px; } 100% { height: 540px; } }
@keyframes helper-rec_mo { 0% { offset-distance: 0%; } 33.3333% { offset-distance: 0%; animation-timing-function: cubic-bezier(0.62,0.079,0.616,1); } 54.5455% { offset-distance: 64.764%; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { offset-distance: 98.323%; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
@keyframes helper-mouse_mo { 0% { offset-distance: 0%; animation-timing-function: cubic-bezier(0.69,0.145,0.37,1); } 18.1818% { offset-distance: 36.981%; } 24.2424% { offset-distance: 38.116%; } 33.3333% { offset-distance: 38.116%; animation-timing-function: cubic-bezier(0.62,0.079,0.616,1); } 54.5455% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
@keyframes helper-boomrightup_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a0_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a1_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a2_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes helper-boomleftup_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a3_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a4_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a5_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes helper-boomrightbottom_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a6_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a7_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a8_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes helper-boomleftbottom_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a9_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a10_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a11_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
    </style>
    <rect id="helper-leftrec" width="260" height="600" fill="#eeeeee" stroke="none" fill-rule="evenodd" rx="12" transform="translate(721.276,336) translate(-27.4763,-124)"/>
    <rect id="helper-rightrec" width="580" height="600" fill="#eeeeee" stroke="none" fill-rule="evenodd" rx="12" transform="translate(139.5,336) translate(-69.5,-124)"/>
    <rect id="helper-rec" width="210" height="100" fill="#ffffff" rx="10" stroke="none" style="transform: translate(-105px,-50px); animation: 3.3s linear infinite both helper-rec_w, 3.3s linear infinite both helper-rec_h, 3.3s linear infinite both helper-rec_mo; offset-path: path('M823.8,300.3L823.8,300.3C649.753,292.487,465.346,303.22,360,512L195,282L205,292L205,292'); offset-rotate: 0deg;"/>
    <g id="helper-mouse" fill="#000000" fill-rule="nonzero" opacity="1" style="transform: scale(2,2) translate(-14px,-14px); animation: 3.3s linear infinite both helper-mouse_mo; offset-path: path('M823.8,616L823.8,290L823.8,300L823.8,300C630.302,283.946,461.204,322.291,360,512L360,512'); offset-rotate: 0deg;">
        <path id="helper-mouse-path" d="M27.9059,13.8023L23.4402,10.278C23.364,10.218,23.2602,10.2069,23.173,10.2494C23.0858,10.2919,23.0305,10.3805,23.0308,10.4775L23.0308,12.7418L15.2614,12.7418L15.2614,4.9723L17.5292,4.9723C17.7392,4.9723,17.8582,4.72732,17.7287,4.56283L14.201,0.0971035C14.1537,0.0358608,14.0806,0,14.0032,0C13.9258,0,13.8528,0.0358608,13.8055,0.0971035L10.2778,4.56283C10.2178,4.63907,10.2067,4.74288,10.2492,4.83009C10.2917,4.91729,10.3803,4.97254,10.4773,4.9723L12.7416,4.9723L12.7416,12.7418L4.97221,12.7418L4.97221,10.474C4.97221,10.264,4.72723,10.145,4.56274,10.2745L0.0971017,13.8023C0.0358601,13.8496,0,13.9226,0,14C0,14.0774,0.0358601,14.1504,0.0971017,14.1977L4.55924,17.7255C4.72373,17.855,4.96871,17.7395,4.96871,17.526L4.96871,15.2617L12.7381,15.2617L12.7381,23.0312L10.4703,23.0312C10.2603,23.0312,10.1413,23.2762,10.2708,23.4407L13.7985,27.9029C13.9,28.0324,14.096,28.0324,14.194,27.9029L17.7217,23.4407C17.8512,23.2762,17.7357,23.0312,17.5222,23.0312L15.2614,23.0312L15.2614,15.2617L23.0308,15.2617L23.0308,17.5295C23.0308,17.7395,23.2757,17.8585,23.4402,17.729L27.9024,14.2012C27.9634,14.1533,27.9993,14.0802,28,14.0026C28.0007,13.925,27.966,13.8513,27.9059,13.8023L27.9059,13.8023Z" transform="translate(14,14) translate(-14,-14)"/>
    </g>
    <g id="helper-boomrightup" opacity="0" transform="translate(678.153,207.1) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-boomrightup_o;">
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a0_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a1_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a2_d;"/>
    </g>
    <g id="helper-boomleftup" opacity="0" transform="translate(65.1,188.323) rotate(-90) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-boomleftup_o;">
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a3_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a4_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a5_d;"/>
    </g>
    <g id="helper-boomrightbottom" opacity="0" transform="translate(652.876,845.127) rotate(90) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-boomrightbottom_o;">
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a6_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a7_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a8_d;"/>
    </g>
    <g id="helper-boomleftbottom" opacity="0" transform="translate(39.8234,819.85) rotate(-180) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-boomleftbottom_o;">
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a9_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a10_d;"/>
        <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a11_d;"/>
    </g>
  `,
  },
  dark: {
    shadow: `
<style>
@keyframes helper-rec_w { 0% { width: 210px; } 54.5455% { width: 210px; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { width: 540px; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { width: 520px; } 100% { width: 520px; } }
@keyframes helper-rec_h { 0% { height: 100px; } 54.5455% { height: 100px; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { height: 560px; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { height: 540px; } 100% { height: 540px; } }
@keyframes helper-rec_mo { 0% { offset-distance: 0%; } 33.3333% { offset-distance: 0%; animation-timing-function: cubic-bezier(0.62,0.079,0.616,1); } 54.5455% { offset-distance: 64.764%; animation-timing-function: cubic-bezier(0.904,0,0.726,0.668); } 66.6667% { offset-distance: 98.323%; animation-timing-function: cubic-bezier(0.214,0.499,0.52,0.867); } 72.7273% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
@keyframes helper-mouse_mo { 0% { offset-distance: 0%; animation-timing-function: cubic-bezier(0.69,0.145,0.37,1); } 18.1818% { offset-distance: 36.981%; } 24.2424% { offset-distance: 38.116%; } 33.3333% { offset-distance: 38.116%; animation-timing-function: cubic-bezier(0.62,0.079,0.616,1); } 54.5455% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
@keyframes helper-boomrightup_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a0_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a1_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a2_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes helper-boomleftup_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a3_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a4_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a5_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes helper-boomrightbottom_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a6_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a7_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a8_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes helper-boomleftbottom_o { 0% { opacity: 0; } 66.6667% { opacity: 0; } 69.697% { opacity: 1; } 75.7576% { opacity: 1; } 78.7879% { opacity: 0; } 100% { opacity: 0; } }
@keyframes a9_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a10_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
@keyframes a11_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 66.6667% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 72.7273% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 78.7879% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
    </style>
    <rect id="helper-leftrec" width="260" height="600" fill="#3b3b3b" stroke="none" fill-rule="evenodd" rx="12" transform="translate(721.276,336) translate(-27.4763,-124)"/>
    <rect id="helper-rightrec" width="580" height="600" fill="#3b3b3b" stroke="none" fill-rule="evenodd" rx="12" transform="translate(139.5,336) translate(-69.5,-124)"/>
    <rect id="helper-rec" width="210" height="100" fill="#1a1a1a" rx="10" stroke="none" style="transform: translate(-105px,-50px); animation: 3.3s linear infinite both helper-rec_w, 3.3s linear infinite both helper-rec_h, 3.3s linear infinite both helper-rec_mo; offset-path: path('M823.8,300.3L823.8,300.3C649.753,292.487,465.346,303.22,360,512L195,282L205,292L205,292'); offset-rotate: 0deg;"/>
    <g id="helper-mouse" fill="#000000" fill-rule="nonzero" opacity="1" style="transform: scale(2,2) translate(-14px,-14px); animation: 3.3s linear infinite both helper-mouse_mo; offset-path: path('M823.8,616L823.8,290L823.8,300L823.8,300C630.302,283.946,461.204,322.291,360,512L360,512'); offset-rotate: 0deg;">
        <path id="helper-mouse-path" d="M27.9059,13.8023L23.4402,10.278C23.364,10.218,23.2602,10.2069,23.173,10.2494C23.0858,10.2919,23.0305,10.3805,23.0308,10.4775L23.0308,12.7418L15.2614,12.7418L15.2614,4.9723L17.5292,4.9723C17.7392,4.9723,17.8582,4.72732,17.7287,4.56283L14.201,0.0971035C14.1537,0.0358608,14.0806,0,14.0032,0C13.9258,0,13.8528,0.0358608,13.8055,0.0971035L10.2778,4.56283C10.2178,4.63907,10.2067,4.74288,10.2492,4.83009C10.2917,4.91729,10.3803,4.97254,10.4773,4.9723L12.7416,4.9723L12.7416,12.7418L4.97221,12.7418L4.97221,10.474C4.97221,10.264,4.72723,10.145,4.56274,10.2745L0.0971017,13.8023C0.0358601,13.8496,0,13.9226,0,14C0,14.0774,0.0358601,14.1504,0.0971017,14.1977L4.55924,17.7255C4.72373,17.855,4.96871,17.7395,4.96871,17.526L4.96871,15.2617L12.7381,15.2617L12.7381,23.0312L10.4703,23.0312C10.2603,23.0312,10.1413,23.2762,10.2708,23.4407L13.7985,27.9029C13.9,28.0324,14.096,28.0324,14.194,27.9029L17.7217,23.4407C17.8512,23.2762,17.7357,23.0312,17.5222,23.0312L15.2614,23.0312L15.2614,15.2617L23.0308,15.2617L23.0308,17.5295C23.0308,17.7395,23.2757,17.8585,23.4402,17.729L27.9024,14.2012C27.9634,14.1533,27.9993,14.0802,28,14.0026C28.0007,13.925,27.966,13.8513,27.9059,13.8023L27.9059,13.8023Z" fill="#ffffff" transform="translate(14,14) translate(-14,-14)"/>
    </g>
    <g id="helper-boomrightup" opacity="0" transform="translate(678.153,207.1) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-boomrightup_o;">
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a0_d;"/>
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a1_d;"/>
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a2_d;"/>
    </g>
    <g id="helper-boomleftup" opacity="0" transform="translate(65.1,188.323) rotate(-90) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-boomleftup_o;">
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a3_d;"/>
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a4_d;"/>
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a5_d;"/>
    </g>
    <g id="helper-boomrightbottom" opacity="0" transform="translate(652.876,845.127) rotate(90) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-boomrightbottom_o;">
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a6_d;"/>
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a7_d;"/>
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a8_d;"/>
    </g>
    <g id="helper-boomleftbottom" opacity="0" transform="translate(39.8234,819.85) rotate(-180) scale(1.3,1.3) translate(-13,-13)" style="animation: 3.3s linear infinite both helper-boomleftbottom_o;">
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a9_d;"/>
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a10_d;"/>
        <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 3.3s linear infinite both a11_d;"/>
    </g>`,
  },
}

export const BatchDragAnimation = {
  light: {
    shadow: `
        <style>
        @keyframes helper-batch-drag-boomrightup_o { 0% { opacity: 0; } 60% { opacity: 0; } 64% { opacity: 1; } 72% { opacity: 1; } 76% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a0_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a1_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a2_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helper-batch-drag-boomleftup_o { 0% { opacity: 0; } 60% { opacity: 0; } 64% { opacity: 1; } 72% { opacity: 1; } 76% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a3_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a4_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a5_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helper-batch-drag-boomrightbottom_o { 0% { opacity: 0; } 60% { opacity: 0; } 64% { opacity: 1; } 72% { opacity: 1; } 76% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a6_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a7_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a8_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helper-batch-drag-boomleftbottom_o { 0% { opacity: 0; } 60% { opacity: 0; } 64% { opacity: 1; } 72% { opacity: 1; } 76% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a9_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a10_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a11_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helpler-dragwhole_t { 0% { transform: scale(1,1) translate(-166.5px,-140px); } 60% { transform: scale(1,1) translate(-166.5px,-140px); } 64% { transform: scale(1.1,1.1) translate(-166.5px,-140px); } 68% { transform: scale(1,1) translate(-166.5px,-140px); } 100% { transform: scale(1,1) translate(-166.5px,-140px); } }
        @keyframes helpler-dragwhole_mo { 0% { offset-distance: 0%; } 20% { offset-distance: 0%; } 28% { offset-distance: 0%; } 40% { offset-distance: 0%; animation-timing-function: cubic-bezier(0.69,0.145,0.37,1); } 68% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
        @keyframes helper-batch-drag-mouse_mo { 0% { offset-distance: 0%; animation-timing-function: cubic-bezier(0.766821,0.127231,0.642631,0.749003); } 20% { offset-distance: 39.885%; animation-timing-function: cubic-bezier(0.232064,0.618697,0.545352,1); } 28% { offset-distance: 41.847%; } 40% { offset-distance: 41.847%; animation-timing-function: cubic-bezier(0.69,0.145,0.37,1); } 68% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
            </style>
            <rect id="helper-batch-drag-rightrec" width="880" height="600" fill="#eeeeee" stroke="none" fill-rule="evenodd" rx="12" transform="translate(441.5,336) translate(-369.5,-124)"/>
            <g id="helper-batch-drag-boomrightup" opacity="0" transform="translate(953.977,487.425) scale(1.3,1.3) translate(-13,-13)" style="animation: 2.5s linear infinite both helper-batch-drag-boomrightup_o;">
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a0_d;"/>
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a1_d;"/>
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a2_d;"/>
            </g>
            <g id="helper-batch-drag-boomleftup" opacity="0" transform="translate(560.35,462.149) rotate(-90) scale(1.3,1.3) translate(-13,-13)" style="animation: 2.5s linear infinite both helper-batch-drag-boomleftup_o;">
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a3_d;"/>
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a4_d;"/>
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a5_d;"/>
            </g>
            <g id="helper-batch-drag-boomrightbottom" opacity="0" transform="translate(928.7,805.302) rotate(90) scale(1.3,1.3) translate(-13,-13)" style="animation: 2.5s linear infinite both helper-batch-drag-boomrightbottom_o;">
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a6_d;"/>
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a7_d;"/>
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a8_d;"/>
            </g>
            <g id="helper-batch-drag-boomleftbottom" opacity="0" transform="translate(535.073,784.75) rotate(-180) scale(1.3,1.3) translate(-13,-13)" style="animation: 2.5s linear infinite both helper-batch-drag-boomleftbottom_o;">
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a9_d;"/>
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a10_d;"/>
                <path fill="#b6b6b6" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a11_d;"/>
            </g>
            <g id="helpler-dragwhole" style="transform: translate(-166.5px,-140px); animation: 2.5s linear infinite both helpler-dragwhole_t, 2.5s linear infinite both helpler-dragwhole_mo; offset-path: path('M286.5,396L286.5,396L286.5,396L286.5,396C470.732,414.82,636.636,472.739,744.5,633.126L744.5,633.126'); offset-rotate: 0deg;">
                <rect id="helper-batch-drag-rec" width="333" height="280" fill="none" rx="10" stroke="#a5a5a5" fill-rule="nonzero" stroke-width="6" stroke-dasharray="12 20" transform="translate(111.034,48.1565) translate(-111.034,-48.1565)"/>
                <rect width="281" height="60" stroke="none" fill="#c4c4c4" stroke-width="2" rx="10" transform="translate(166.5,53) translate(-140.5,-30)"/>
                <rect width="140.5" height="60" stroke="none" fill="#c4c4c4" stroke-width="2" rx="10" transform="translate(166.5,137.525) translate(-140.5,-30)"/>
                <rect width="190.054" height="60" stroke="none" fill="#c4c4c4" stroke-width="2" rx="10" transform="translate(166.5,222.051) translate(-140.5,-30)"/>
            </g>
            <g id="helper-batch-drag-mouse" fill="#000000" fill-rule="nonzero" opacity="1" style="transform: scale(2,2) translate(-14px,-14px); animation: 2.5s linear infinite both helper-batch-drag-mouse_mo; offset-path: path('M286.5,744L286.5,378L286.5,396L286.5,396C474.045,416.166,638.679,477.076,744.5,633.126L744.5,633.126'); offset-rotate: 0deg;">
                <path id="helper-batch-drag-mouse-path" d="M27.9059,13.8023L23.4402,10.278C23.364,10.218,23.2602,10.2069,23.173,10.2494C23.0858,10.2919,23.0305,10.3805,23.0308,10.4775L23.0308,12.7418L15.2614,12.7418L15.2614,4.9723L17.5292,4.9723C17.7392,4.9723,17.8582,4.72732,17.7287,4.56283L14.201,0.0971035C14.1537,0.0358608,14.0806,0,14.0032,0C13.9258,0,13.8528,0.0358608,13.8055,0.0971035L10.2778,4.56283C10.2178,4.63907,10.2067,4.74288,10.2492,4.83009C10.2917,4.91729,10.3803,4.97254,10.4773,4.9723L12.7416,4.9723L12.7416,12.7418L4.97221,12.7418L4.97221,10.474C4.97221,10.264,4.72723,10.145,4.56274,10.2745L0.0971017,13.8023C0.0358601,13.8496,0,13.9226,0,14C0,14.0774,0.0358601,14.1504,0.0971017,14.1977L4.55924,17.7255C4.72373,17.855,4.96871,17.7395,4.96871,17.526L4.96871,15.2617L12.7381,15.2617L12.7381,23.0312L10.4703,23.0312C10.2603,23.0312,10.1413,23.2762,10.2708,23.4407L13.7985,27.9029C13.9,28.0324,14.096,28.0324,14.194,27.9029L17.7217,23.4407C17.8512,23.2762,17.7357,23.0312,17.5222,23.0312L15.2614,23.0312L15.2614,15.2617L23.0308,15.2617L23.0308,17.5295C23.0308,17.7395,23.2757,17.8585,23.4402,17.729L27.9024,14.2012C27.9634,14.1533,27.9993,14.0802,28,14.0026C28.0007,13.925,27.966,13.8513,27.9059,13.8023L27.9059,13.8023Z" transform="translate(14,14) translate(-14,-14)"/>
            </g>
          `,
  },
  dark: {
    shadow: `
        <style>
        @keyframes helper-batch-drag-boomrightup_o { 0% { opacity: 0; } 60% { opacity: 0; } 64% { opacity: 1; } 72% { opacity: 1; } 76% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a0_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a1_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a2_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helper-batch-drag-boomleftup_o { 0% { opacity: 0; } 60% { opacity: 0; } 64% { opacity: 1; } 72% { opacity: 1; } 76% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a3_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a4_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a5_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helper-batch-drag-boomrightbottom_o { 0% { opacity: 0; } 60% { opacity: 0; } 64% { opacity: 1; } 72% { opacity: 1; } 76% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a6_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a7_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a8_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helper-batch-drag-boomleftbottom_o { 0% { opacity: 0; } 60% { opacity: 0; } 64% { opacity: 1; } 72% { opacity: 1; } 76% { opacity: 0; } 100% { opacity: 0; } }
        @keyframes a9_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a10_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes a11_d { 0% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); } 60% { d: path('M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 68% { d: path('M1,-15L3,-15C3.55228,-15,4,-14.5523,4,-14L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,-14C0,-14.5523,0.447715,-15,1,-15Z'); animation-timing-function: cubic-bezier(0.455,0.03,0.515,0.955); } 76% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } 100% { d: path('M1,-10L3,-10C3.55228,-10,4,-9.55228,4,-9L4,-7C4,-6.44772,3.55228,-6,3,-6L1,-6C0.447715,-6,0,-6.44772,0,-7L0,-9C0,-9.55228,0.447715,-10,1,-10Z'); } }
        @keyframes helpler-dragwhole_t { 0% { transform: scale(1,1) translate(-166.5px,-140px); } 60% { transform: scale(1,1) translate(-166.5px,-140px); } 64% { transform: scale(1.1,1.1) translate(-166.5px,-140px); } 68% { transform: scale(1,1) translate(-166.5px,-140px); } 100% { transform: scale(1,1) translate(-166.5px,-140px); } }
        @keyframes helpler-dragwhole_mo { 0% { offset-distance: 0%; } 20% { offset-distance: 0%; } 28% { offset-distance: 0%; } 40% { offset-distance: 0%; animation-timing-function: cubic-bezier(0.69,0.145,0.37,1); } 68% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
        @keyframes helper-batch-drag-mouse_mo { 0% { offset-distance: 0%; animation-timing-function: cubic-bezier(0.766821,0.127231,0.642631,0.749003); } 20% { offset-distance: 39.885%; animation-timing-function: cubic-bezier(0.232064,0.618697,0.545352,1); } 28% { offset-distance: 41.847%; } 40% { offset-distance: 41.847%; animation-timing-function: cubic-bezier(0.69,0.145,0.37,1); } 68% { offset-distance: 100%; } 100% { offset-distance: 100%; } }
            </style>
            <rect id="helper-batch-drag-rightrec" width="880" height="600" fill="#3b3b3b" stroke="none" fill-rule="evenodd" rx="12" transform="translate(441.5,336) translate(-369.5,-124)"/>
            <g id="helper-batch-drag-boomrightup" opacity="0" transform="translate(953.977,487.425) scale(1.3,1.3) translate(-13,-13)" style="animation: 2.5s linear infinite both helper-batch-drag-boomrightup_o;">
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a0_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a1_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a2_d;"/>
            </g>
            <g id="helper-batch-drag-boomleftup" opacity="0" transform="translate(560.35,462.149) rotate(-90) scale(1.3,1.3) translate(-13,-13)" style="animation: 2.5s linear infinite both helper-batch-drag-boomleftup_o;">
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a3_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a4_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a5_d;"/>
            </g>
            <g id="helper-batch-drag-boomrightbottom" opacity="0" transform="translate(928.7,805.302) rotate(90) scale(1.3,1.3) translate(-13,-13)" style="animation: 2.5s linear infinite both helper-batch-drag-boomrightbottom_o;">
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a6_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a7_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a8_d;"/>
            </g>
            <g id="helper-batch-drag-boomleftbottom" opacity="0" transform="translate(535.073,784.75) rotate(-180) scale(1.3,1.3) translate(-13,-13)" style="animation: 2.5s linear infinite both helper-batch-drag-boomleftbottom_o;">
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a9_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(45) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a10_d;"/>
                <path fill="#808080" opacity="1" stroke="none" d="M1,0L3,0C3.55228,0,4,0.447715,4,1L4,3C4,3.55228,3.55228,4,3,4L1,4C0.447715,4,0,3.55228,0,3L0,1C0,0.447715,0.447715,0,1,0Z" transform="translate(-15.4435,22) rotate(90) scale(2,2) translate(-2,-14)" style="animation: 2.5s linear infinite both a11_d;"/>
            </g>
            <g id="helpler-dragwhole" style="transform: translate(-166.5px,-140px); animation: 2.5s linear infinite both helpler-dragwhole_t, 2.5s linear infinite both helpler-dragwhole_mo; offset-path: path('M286.5,396L286.5,396L286.5,396L286.5,396C470.732,414.82,636.636,472.739,744.5,633.126L744.5,633.126'); offset-rotate: 0deg;">
                <rect id="helper-batch-drag-rec" width="333" height="280" fill="none" rx="10" stroke="#151515" fill-rule="nonzero" stroke-width="6" stroke-dasharray="12 20" transform="translate(111.034,48.1565) translate(-111.034,-48.1565)"/>
                <rect width="281" height="60" stroke="none" fill="#1a1a1a" stroke-width="2" rx="10" transform="translate(166.5,53) translate(-140.5,-30)"/>
                <rect width="140.5" height="60" stroke="none" fill="#1a1a1a" stroke-width="2" rx="10" transform="translate(166.5,137.525) translate(-140.5,-30)"/>
                <rect width="190.054" height="60" stroke="none" fill="#1a1a1a" stroke-width="2" rx="10" transform="translate(166.5,222.051) translate(-140.5,-30)"/>
            </g>
            <g id="helper-batch-drag-mouse" fill="#000000" fill-rule="nonzero" opacity="1" style="transform: scale(2,2) translate(-14px,-14px); animation: 2.5s linear infinite both helper-batch-drag-mouse_mo; offset-path: path('M286.5,744L286.5,378L286.5,396L286.5,396C474.045,416.166,638.679,477.076,744.5,633.126L744.5,633.126'); offset-rotate: 0deg;">
                <path id="helper-batch-drag-mouse-path" d="M27.9059,13.8023L23.4402,10.278C23.364,10.218,23.2602,10.2069,23.173,10.2494C23.0858,10.2919,23.0305,10.3805,23.0308,10.4775L23.0308,12.7418L15.2614,12.7418L15.2614,4.9723L17.5292,4.9723C17.7392,4.9723,17.8582,4.72732,17.7287,4.56283L14.201,0.0971035C14.1537,0.0358608,14.0806,0,14.0032,0C13.9258,0,13.8528,0.0358608,13.8055,0.0971035L10.2778,4.56283C10.2178,4.63907,10.2067,4.74288,10.2492,4.83009C10.2917,4.91729,10.3803,4.97254,10.4773,4.9723L12.7416,4.9723L12.7416,12.7418L4.97221,12.7418L4.97221,10.474C4.97221,10.264,4.72723,10.145,4.56274,10.2745L0.0971017,13.8023C0.0358601,13.8496,0,13.9226,0,14C0,14.0774,0.0358601,14.1504,0.0971017,14.1977L4.55924,17.7255C4.72373,17.855,4.96871,17.7395,4.96871,17.526L4.96871,15.2617L12.7381,15.2617L12.7381,23.0312L10.4703,23.0312C10.2603,23.0312,10.1413,23.2762,10.2708,23.4407L13.7985,27.9029C13.9,28.0324,14.096,28.0324,14.194,27.9029L17.7217,23.4407C17.8512,23.2762,17.7357,23.0312,17.5222,23.0312L15.2614,23.0312L15.2614,15.2617L23.0308,15.2617L23.0308,17.5295C23.0308,17.7395,23.2757,17.8585,23.4402,17.729L27.9024,14.2012C27.9634,14.1533,27.9993,14.0802,28,14.0026C28.0007,13.925,27.966,13.8513,27.9059,13.8023L27.9059,13.8023Z" fill="#ffffff" transform="translate(14,14) translate(-14,-14)"/>
            </g>
            `,
  },
}
