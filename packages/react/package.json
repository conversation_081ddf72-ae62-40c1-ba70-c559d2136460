{"name": "@designable/react", "version": "1.0.0-beta.45", "license": "MIT", "main": "lib", "types": "lib/index.d.ts", "engines": {"npm": ">=3.0.0"}, "module": "esm", "repository": {"type": "git", "url": "git+https://github.com/alibaba/designable.git"}, "bugs": {"url": "https://github.com/alibaba/designable/issues"}, "homepage": "https://github.com/alibaba/designable#readme", "scripts": {"build": "rimraf -rf lib esm dist && npm run build:cjs && npm run build:esm", "build:cjs": "tsc --project tsconfig.build.json", "build:esm": "tsc --project tsconfig.build.json --module es2015 --outDir esm", "build:umd": "rollup --config"}, "devDependencies": {"@formily/reactive": "^2.0.2", "@formily/reactive-react": "^2.0.2"}, "peerDependencies": {"@formily/reactive": "^2.0.2", "@formily/reactive-react": "^2.0.2", "antd": "^4.15.4", "react": "16.x || 17.x"}, "dependencies": {"@designable/core": "1.0.0-beta.45", "@designable/shared": "1.0.0-beta.45", "dateformat": "^4.5.1"}, "publishConfig": {"access": "public"}, "gitHead": "bda070c137ba0003cc4451b2208e089d2e326b23"}