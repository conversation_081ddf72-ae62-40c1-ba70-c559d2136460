{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./lib", "paths": {"@designable/*": ["../*"]}, "declaration": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "skipLibCheck": true, "suppressImplicitAnyIndexErrors": true, "noStrictGenericChecks": true}, "ts-node": {"compilerOptions": {"module": "commonjs"}}}